# Unity 太阳系模拟器

这是一个在Unity中创建的太阳系模拟场景，包含了太阳、八大行星以及它们的真实轨道运动。

## 功能特性

### 🌟 核心功能
- **真实太阳系数据**: 基于真实的行星轨道距离、公转周期、自转周期等数据
- **动态轨道运动**: 行星按照真实比例进行公转和自转
- **时间控制**: 可调节时间速度，支持暂停/继续
- **多种相机模式**: 自由模式、跟随模式、轨道模式、总览模式

### 🎮 交互控制
- **WASD**: 相机移动（自由模式下）
- **鼠标拖拽**: 旋转视角
- **滚轮**: 缩放视野
- **空格键**: 暂停/继续模拟
- **Tab键**: 切换跟随目标
- **R键**: 开启/关闭自动旋转
- **F1-F4**: 切换相机模式
- **H键**: 显示/隐藏UI
- **数字键1-6**: 快速调节时间速度

### 🎨 视觉效果
- **太阳发光效果**: 太阳具有发光材质和脉冲效果
- **行星材质**: 每个行星都有独特的颜色和材质
- **轨道轨迹**: 可视化行星运行轨道
- **星空背景**: 逼真的太空环境

## 快速开始

### 1. 自动设置
1. 在场景中创建一个空的GameObject
2. 添加 `SolarSystemSetup` 脚本
3. 勾选 `Auto Setup On Start`
4. 运行场景，系统会自动创建完整的太阳系

### 2. 手动设置
1. 创建 `SolarSystemManager` GameObject并添加对应脚本
2. 创建太阳和行星的预制体（球体）
3. 为太阳和行星创建材质
4. 在SolarSystemManager中配置预制体和材质
5. 添加 `SolarSystemCamera` 脚本到主相机
6. 创建UI Canvas并添加 `SolarSystemUI` 脚本

## 脚本说明

### 核心脚本

#### `CelestialBody.cs`
- 天体基类，包含所有天体的基本属性和行为
- 处理轨道运动、自转、材质设置等

#### `Sun.cs`
- 太阳类，继承自CelestialBody
- 包含发光效果、光源设置、脉冲动画等

#### `Planet.cs`
- 行星类，继承自CelestialBody
- 支持卫星系统、大气层、光环等特性

#### `SolarSystemManager.cs`
- 太阳系主管理器
- 控制整个太阳系的创建、时间缩放、暂停等

### 控制脚本

#### `SolarSystemCamera.cs`
- 相机控制器，提供多种观察模式
- 支持自由移动、跟随目标、轨道环绕等

#### `SolarSystemUI.cs`
- UI控制器，提供用户界面
- 包含时间控制、相机模式切换、信息显示等

#### `SolarSystemSetup.cs`
- 快速设置工具
- 自动创建太阳系所需的所有组件和资源

## 自定义配置

### 修改行星数据
在 `SolarSystemManager.cs` 中的 `realPlanetData` 数组中修改行星参数：
```csharp
new PlanetData("行星名", 轨道距离AU, 公转周期年, 自转周期天, 轴倾斜度, 半径比例)
```

### 添加新行星
1. 在 `realPlanetData` 数组中添加新的行星数据
2. 在 `planetColors` 数组中添加对应的颜色
3. 创建对应的材质

### 自定义材质
- 太阳：使用发光材质（Emission）
- 行星：使用标准材质，可调节金属度和光滑度
- 特殊效果：可添加法线贴图、高度图等

## 性能优化

### 建议设置
- **缩放因子**: 调节 `scaleFactor` 来平衡视觉效果和性能
- **LOD系统**: 为远距离天体使用简化模型
- **材质优化**: 使用移动端友好的着色器
- **更新频率**: 可以降低Update频率来提升性能

### 大型太阳系
对于包含更多天体的大型太阳系：
- 使用对象池管理天体
- 实现视锥剔除
- 使用异步加载
- 分层渲染

## 扩展功能

### 可添加的功能
- **卫星系统**: 为行星添加卫星
- **小行星带**: 在火星和木星之间添加小行星
- **彗星**: 添加椭圆轨道的彗星
- **星座**: 添加背景星座
- **时间轴**: 显示历史和未来的天体位置
- **物理模拟**: 使用真实的引力计算

### 教育功能
- **信息面板**: 显示详细的天体信息
- **比较工具**: 对比不同行星的大小和特性
- **任务系统**: 添加探索任务和挑战
- **VR支持**: 支持VR设备进行沉浸式体验

## 故障排除

### 常见问题
1. **天体不运动**: 检查时间缩放是否为0，确保没有暂停
2. **材质显示异常**: 确保使用了正确的着色器和材质设置
3. **相机控制失效**: 检查相机脚本是否正确添加和配置
4. **UI不显示**: 确保Canvas设置正确，UI脚本已添加

### 调试技巧
- 使用Scene视图观察天体位置
- 在Inspector中实时调节参数
- 使用Debug.Log输出关键信息
- 检查Console中的错误信息

## 📁 文件结构

```
Assets/Scripts/SolarSystem/
├── CelestialBody.cs          # 天体基类
├── Sun.cs                    # 太阳类
├── Planet.cs                 # 行星类
├── SolarSystemManager.cs     # 主管理器
├── SolarSystemCamera.cs      # 相机控制器
├── SolarSystemUI.cs          # UI控制器
├── SolarSystemSetup.cs       # 快速设置工具
├── SolarSystemDemo.cs        # 演示脚本
├── OrbitVisualizer.cs        # 轨道可视化
├── MaterialCreator.cs        # 材质创建器
├── Editor/
│   └── SolarSystemEditor.cs  # 编辑器工具
├── README.md                 # 主要说明文档
├── 使用指南.md               # 中文使用指南
└── 其他文档...
```

## 🎯 完整功能列表

### ✅ 已实现功能
- [x] 真实太阳系数据模拟
- [x] 八大行星轨道运动
- [x] 太阳发光效果
- [x] 多种相机模式
- [x] 时间控制系统
- [x] 轨道可视化
- [x] 交互式UI界面
- [x] 自动演示模式
- [x] 材质管理系统
- [x] 编辑器工具
- [x] 快速设置功能

### 🔄 可扩展功能
- [ ] 卫星系统（月球等）
- [ ] 小行星带
- [ ] 彗星轨道
- [ ] 星座背景
- [ ] VR支持
- [ ] 物理引力模拟
- [ ] 音效系统
- [ ] 多语言支持

## 🚀 快速部署

### 一键部署
1. 将所有脚本文件复制到Unity项目中
2. 在场景中创建空GameObject
3. 添加`SolarSystemSetup`脚本
4. 勾选`Auto Setup On Start`
5. 点击Play - 完成！

### 编辑器部署
1. 使用菜单`Tools > Solar System > Solar System Editor`
2. 点击"创建完整太阳系"
3. 自动生成所有组件和资源

## 版本信息
- Unity版本: 2021.3 LTS 或更高
- 支持平台: Windows, Mac, Linux
- 依赖: TextMeshPro (UI文本显示)
- 脚本语言: C#
- 渲染管线: Built-in RP (可扩展至URP/HDRP)

## 许可证
此项目仅供学习和教育用途使用。

---

🌟 **恭喜！您已经拥有了一个完整的Unity太阳系模拟器！** 🌟

享受探索宇宙的乐趣！🚀✨
