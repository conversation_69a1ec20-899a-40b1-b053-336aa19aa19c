using UnityEngine;
using UnityEngine.UI;

namespace SolarSystem
{
    /// <summary>
    /// 太阳系快速设置工具
    /// </summary>
    public class SolarSystemSetup : MonoBehaviour
    {
        [Header("自动设置")]
        public bool autoSetupOnStart = true;
        public bool createBasicMaterials = true;
        public bool createUI = true;
        
        [Header("天体预制体")]
        public GameObject spherePrefab;
        
        [Header("材质颜色")]
        public Color sunColor = Color.yellow;
        public Color[] planetColors = new Color[]
        {
            new Color(0.7f, 0.7f, 0.7f), // 水星 - 灰色
            new Color(1f, 0.8f, 0.4f),   // 金星 - 橙黄色
            new Color(0.2f, 0.6f, 1f),   // 地球 - 蓝色
            new Color(0.8f, 0.4f, 0.2f), // 火星 - 红色
            new Color(0.9f, 0.7f, 0.4f), // 木星 - 橙色
            new Color(0.9f, 0.8f, 0.6f), // 土星 - 淡黄色
            new Color(0.4f, 0.8f, 0.9f), // 天王星 - 青色
            new Color(0.2f, 0.4f, 0.8f)  // 海王星 - 蓝色
        };
        
        void Start()
        {
            if (autoSetupOnStart)
            {
                SetupSolarSystem();
            }
        }
        
        /// <summary>
        /// 设置太阳系
        /// </summary>
        [ContextMenu("Setup Solar System")]
        public void SetupSolarSystem()
        {
            // 创建基础材质
            if (createBasicMaterials)
            {
                CreateBasicMaterials();
            }
            
            // 设置太阳系管理器
            SetupSolarSystemManager();
            
            // 设置相机
            SetupCamera();
            
            // 创建UI
            if (createUI)
            {
                SetupUI();
            }
            
            Debug.Log("太阳系设置完成！");
        }
        
        /// <summary>
        /// 创建基础材质
        /// </summary>
        private void CreateBasicMaterials()
        {
            // 创建材质文件夹
            string materialPath = "Assets/Materials/SolarSystem";
            if (!System.IO.Directory.Exists(materialPath))
            {
                System.IO.Directory.CreateDirectory(materialPath);
            }
            
            // 创建太阳材质
            Material sunMaterial = CreateEmissiveMaterial("SunMaterial", sunColor);
            
            // 创建行星材质
            Material[] planetMaterials = new Material[planetColors.Length];
            string[] planetNames = { "Mercury", "Venus", "Earth", "Mars", "Jupiter", "Saturn", "Uranus", "Neptune" };
            
            for (int i = 0; i < planetColors.Length; i++)
            {
                planetMaterials[i] = CreateStandardMaterial(planetNames[i] + "Material", planetColors[i]);
            }
        }
        
        /// <summary>
        /// 创建发光材质
        /// </summary>
        private Material CreateEmissiveMaterial(string name, Color color)
        {
            Material material = new Material(Shader.Find("Standard"));
            material.name = name;
            material.SetColor("_Color", color);
            material.SetColor("_EmissionColor", color * 2f);
            material.EnableKeyword("_EMISSION");
            material.globalIlluminationFlags = MaterialGlobalIlluminationFlags.RealtimeEmissive;
            
            return material;
        }
        
        /// <summary>
        /// 创建标准材质
        /// </summary>
        private Material CreateStandardMaterial(string name, Color color)
        {
            Material material = new Material(Shader.Find("Standard"));
            material.name = name;
            material.SetColor("_Color", color);
            material.SetFloat("_Metallic", 0.1f);
            material.SetFloat("_Smoothness", 0.3f);
            
            return material;
        }
        
        /// <summary>
        /// 设置太阳系管理器
        /// </summary>
        private void SetupSolarSystemManager()
        {
            GameObject managerObj = GameObject.Find("SolarSystemManager");
            if (managerObj == null)
            {
                managerObj = new GameObject("SolarSystemManager");
            }
            
            SolarSystemManager manager = managerObj.GetComponent<SolarSystemManager>();
            if (manager == null)
            {
                manager = managerObj.AddComponent<SolarSystemManager>();
            }
            
            // 设置预制体
            if (spherePrefab != null)
            {
                manager.sunPrefab = spherePrefab;
                manager.planetPrefab = spherePrefab;
                manager.moonPrefab = spherePrefab;
            }
            else
            {
                // 创建基础球体预制体
                CreateBasicSpherePrefab();
            }
        }
        
        /// <summary>
        /// 创建基础球体预制体
        /// </summary>
        private void CreateBasicSpherePrefab()
        {
            GameObject sphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            sphere.name = "CelestialBodyPrefab";
            
            // 移除默认的碰撞器
            Collider collider = sphere.GetComponent<Collider>();
            if (collider) DestroyImmediate(collider);
            
            // 保存为预制体
            string prefabPath = "Assets/Prefabs/SolarSystem";
            if (!System.IO.Directory.Exists(prefabPath))
            {
                System.IO.Directory.CreateDirectory(prefabPath);
            }
            
            #if UNITY_EDITOR
            UnityEditor.PrefabUtility.SaveAsPrefabAsset(sphere, prefabPath + "/CelestialBodyPrefab.prefab");
            #endif
            
            DestroyImmediate(sphere);
        }
        
        /// <summary>
        /// 设置相机
        /// </summary>
        private void SetupCamera()
        {
            Camera mainCamera = Camera.main;
            if (mainCamera == null)
            {
                GameObject cameraObj = new GameObject("Main Camera");
                mainCamera = cameraObj.AddComponent<Camera>();
                cameraObj.AddComponent<AudioListener>();
                cameraObj.tag = "MainCamera";
            }
            
            // 添加太阳系相机控制器
            SolarSystemCamera cameraController = mainCamera.GetComponent<SolarSystemCamera>();
            if (cameraController == null)
            {
                cameraController = mainCamera.gameObject.AddComponent<SolarSystemCamera>();
            }
            
            // 设置初始位置
            mainCamera.transform.position = new Vector3(0, 10, -50);
            mainCamera.transform.LookAt(Vector3.zero);
        }
        
        /// <summary>
        /// 设置UI
        /// </summary>
        private void SetupUI()
        {
            // 查找或创建Canvas
            Canvas canvas = FindObjectOfType<Canvas>();
            if (canvas == null)
            {
                GameObject canvasObj = new GameObject("Canvas");
                canvas = canvasObj.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvasObj.AddComponent<CanvasScaler>();
                canvasObj.AddComponent<GraphicRaycaster>();
            }
            
            // 创建UI管理器
            GameObject uiManagerObj = new GameObject("SolarSystemUI");
            uiManagerObj.transform.SetParent(canvas.transform);
            SolarSystemUI uiManager = uiManagerObj.AddComponent<SolarSystemUI>();
            
            // 创建基础UI面板
            CreateBasicUI(canvas.transform, uiManager);
        }
        
        /// <summary>
        /// 创建基础UI
        /// </summary>
        private void CreateBasicUI(Transform parent, SolarSystemUI uiManager)
        {
            // 创建控制面板
            GameObject controlPanel = CreateUIPanel("ControlPanel", parent, new Vector2(300, 200), new Vector2(10, 10));
            uiManager.controlPanel = controlPanel;
            
            // 创建信息面板
            GameObject infoPanel = CreateUIPanel("InfoPanel", parent, new Vector2(250, 300), new Vector2(-10, 10));
            uiManager.infoPanel = infoPanel;
            
            // 在控制面板中添加基础控件
            CreateBasicControls(controlPanel.transform, uiManager);
            
            // 在信息面板中添加文本
            CreateInfoText(infoPanel.transform, uiManager);
        }
        
        /// <summary>
        /// 创建UI面板
        /// </summary>
        private GameObject CreateUIPanel(string name, Transform parent, Vector2 size, Vector2 anchoredPosition)
        {
            GameObject panel = new GameObject(name);
            panel.transform.SetParent(parent);
            
            RectTransform rectTransform = panel.AddComponent<RectTransform>();
            rectTransform.sizeDelta = size;
            rectTransform.anchoredPosition = anchoredPosition;
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.zero;
            
            Image image = panel.AddComponent<Image>();
            image.color = new Color(0, 0, 0, 0.7f);
            
            return panel;
        }
        
        /// <summary>
        /// 创建基础控件
        /// </summary>
        private void CreateBasicControls(Transform parent, SolarSystemUI uiManager)
        {
            // 这里可以添加滑块、按钮等控件的创建代码
            // 由于UI创建比较复杂，建议在Unity编辑器中手动创建UI
            
            // 创建简单的文本说明
            GameObject instructionText = new GameObject("Instructions");
            instructionText.transform.SetParent(parent);
            
            Text text = instructionText.AddComponent<Text>();
            text.text = "太阳系模拟器\n\n操作说明:\nWASD - 移动\n鼠标 - 旋转\n滚轮 - 缩放\n空格 - 暂停";
            text.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
            text.fontSize = 12;
            text.color = Color.white;
            
            RectTransform textRect = instructionText.GetComponent<RectTransform>();
            textRect.sizeDelta = new Vector2(280, 180);
            textRect.anchoredPosition = Vector2.zero;
            
            uiManager.instructionsText = text;
        }
        
        /// <summary>
        /// 创建信息文本
        /// </summary>
        private void CreateInfoText(Transform parent, SolarSystemUI uiManager)
        {
            GameObject infoText = new GameObject("PlanetInfo");
            infoText.transform.SetParent(parent);
            
            Text text = infoText.AddComponent<Text>();
            text.text = "选择一个天体查看信息";
            text.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
            text.fontSize = 11;
            text.color = Color.white;
            
            RectTransform textRect = infoText.GetComponent<RectTransform>();
            textRect.sizeDelta = new Vector2(230, 280);
            textRect.anchoredPosition = Vector2.zero;
            
            uiManager.planetInfoText = text;
        }
        
        /// <summary>
        /// 清理太阳系
        /// </summary>
        [ContextMenu("Clear Solar System")]
        public void ClearSolarSystem()
        {
            // 删除所有太阳系相关对象
            SolarSystemManager[] managers = FindObjectsOfType<SolarSystemManager>();
            foreach (var manager in managers)
            {
                DestroyImmediate(manager.gameObject);
            }
            
            CelestialBody[] bodies = FindObjectsOfType<CelestialBody>();
            foreach (var body in bodies)
            {
                DestroyImmediate(body.gameObject);
            }
            
            Debug.Log("太阳系已清理！");
        }
    }
}
