using UnityEngine;

namespace SolarSystem
{
    /// <summary>
    /// 轨道可视化器，用于显示天体的运行轨道
    /// </summary>
    [RequireComponent(typeof(LineRenderer))]
    public class OrbitVisualizer : MonoBehaviour
    {
        [Header("轨道设置")]
        public Transform orbitCenter;
        public float orbitRadius = 10f;
        public int orbitSegments = 100;
        public bool showOrbit = true;
        
        [Header("视觉设置")]
        public Color orbitColor = Color.white;
        public float lineWidth = 0.1f;
        public Material orbitMaterial;
        public bool useGradient = false;
        public Gradient orbitGradient;
        
        [Header("动画设置")]
        public bool animateOrbit = false;
        public float animationSpeed = 1f;
        public bool fadeWithDistance = true;
        public float maxVisibleDistance = 100f;
        
        private LineRenderer lineRenderer;
        private Vector3[] orbitPoints;
        private Camera mainCamera;
        private float animationTime = 0f;
        
        void Start()
        {
            InitializeOrbitVisualizer();
        }
        
        void Update()
        {
            if (showOrbit)
            {
                UpdateOrbitVisualization();
            }
        }
        
        /// <summary>
        /// 初始化轨道可视化器
        /// </summary>
        private void InitializeOrbitVisualizer()
        {
            lineRenderer = GetComponent<LineRenderer>();
            mainCamera = Camera.main;
            
            SetupLineRenderer();
            GenerateOrbitPoints();
            UpdateOrbitLine();
        }
        
        /// <summary>
        /// 设置LineRenderer
        /// </summary>
        private void SetupLineRenderer()
        {
            lineRenderer.useWorldSpace = true;
            lineRenderer.loop = true;
            lineRenderer.positionCount = orbitSegments;
            lineRenderer.startWidth = lineWidth;
            lineRenderer.endWidth = lineWidth;
            lineRenderer.color = orbitColor;
            
            if (orbitMaterial)
            {
                lineRenderer.material = orbitMaterial;
            }
            else
            {
                // 创建默认材质
                Material defaultMaterial = new Material(Shader.Find("Sprites/Default"));
                defaultMaterial.color = orbitColor;
                lineRenderer.material = defaultMaterial;
            }
            
            if (useGradient && orbitGradient != null)
            {
                lineRenderer.colorGradient = orbitGradient;
            }
        }
        
        /// <summary>
        /// 生成轨道点
        /// </summary>
        private void GenerateOrbitPoints()
        {
            orbitPoints = new Vector3[orbitSegments];
            
            Vector3 centerPosition = orbitCenter ? orbitCenter.position : Vector3.zero;
            
            for (int i = 0; i < orbitSegments; i++)
            {
                float angle = (float)i / orbitSegments * 2f * Mathf.PI;
                float x = Mathf.Cos(angle) * orbitRadius;
                float z = Mathf.Sin(angle) * orbitRadius;
                
                orbitPoints[i] = centerPosition + new Vector3(x, 0, z);
            }
        }
        
        /// <summary>
        /// 更新轨道线
        /// </summary>
        private void UpdateOrbitLine()
        {
            if (orbitPoints != null && orbitPoints.Length > 0)
            {
                lineRenderer.SetPositions(orbitPoints);
            }
        }
        
        /// <summary>
        /// 更新轨道可视化
        /// </summary>
        private void UpdateOrbitVisualization()
        {
            // 更新轨道中心位置
            if (orbitCenter)
            {
                Vector3 centerPosition = orbitCenter.position;
                for (int i = 0; i < orbitPoints.Length; i++)
                {
                    float angle = (float)i / orbitSegments * 2f * Mathf.PI;
                    float x = Mathf.Cos(angle) * orbitRadius;
                    float z = Mathf.Sin(angle) * orbitRadius;
                    
                    orbitPoints[i] = centerPosition + new Vector3(x, 0, z);
                }
                
                UpdateOrbitLine();
            }
            
            // 动画效果
            if (animateOrbit)
            {
                UpdateOrbitAnimation();
            }
            
            // 距离淡化
            if (fadeWithDistance && mainCamera)
            {
                UpdateDistanceFading();
            }
            
            // 更新可见性
            lineRenderer.enabled = showOrbit;
        }
        
        /// <summary>
        /// 更新轨道动画
        /// </summary>
        private void UpdateOrbitAnimation()
        {
            animationTime += Time.deltaTime * animationSpeed;
            
            // 创建流动效果
            if (orbitMaterial && orbitMaterial.HasProperty("_MainTex"))
            {
                Vector2 offset = orbitMaterial.mainTextureOffset;
                offset.x = animationTime % 1f;
                orbitMaterial.mainTextureOffset = offset;
            }
        }
        
        /// <summary>
        /// 更新距离淡化
        /// </summary>
        private void UpdateDistanceFading()
        {
            float distance = Vector3.Distance(mainCamera.transform.position, transform.position);
            float alpha = 1f - Mathf.Clamp01(distance / maxVisibleDistance);
            
            Color currentColor = lineRenderer.color;
            currentColor.a = alpha * orbitColor.a;
            lineRenderer.color = currentColor;
        }
        
        /// <summary>
        /// 设置轨道参数
        /// </summary>
        public void SetOrbitParameters(Transform center, float radius)
        {
            orbitCenter = center;
            orbitRadius = radius;
            
            GenerateOrbitPoints();
            UpdateOrbitLine();
        }
        
        /// <summary>
        /// 设置轨道颜色
        /// </summary>
        public void SetOrbitColor(Color color)
        {
            orbitColor = color;
            lineRenderer.color = color;
            
            if (lineRenderer.material)
            {
                lineRenderer.material.color = color;
            }
        }
        
        /// <summary>
        /// 设置轨道宽度
        /// </summary>
        public void SetOrbitWidth(float width)
        {
            lineWidth = width;
            lineRenderer.startWidth = width;
            lineRenderer.endWidth = width;
        }
        
        /// <summary>
        /// 显示轨道
        /// </summary>
        public void ShowOrbit()
        {
            showOrbit = true;
            lineRenderer.enabled = true;
        }
        
        /// <summary>
        /// 隐藏轨道
        /// </summary>
        public void HideOrbit()
        {
            showOrbit = false;
            lineRenderer.enabled = false;
        }
        
        /// <summary>
        /// 切换轨道显示
        /// </summary>
        public void ToggleOrbit()
        {
            showOrbit = !showOrbit;
            lineRenderer.enabled = showOrbit;
        }
        
        /// <summary>
        /// 设置轨道段数
        /// </summary>
        public void SetOrbitSegments(int segments)
        {
            orbitSegments = Mathf.Max(3, segments);
            lineRenderer.positionCount = orbitSegments;
            
            GenerateOrbitPoints();
            UpdateOrbitLine();
        }
        
        /// <summary>
        /// 创建轨道可视化器
        /// </summary>
        public static OrbitVisualizer CreateOrbitVisualizer(GameObject target, Transform center, float radius)
        {
            GameObject orbitObj = new GameObject(target.name + "_Orbit");
            orbitObj.transform.SetParent(target.transform);
            orbitObj.transform.localPosition = Vector3.zero;
            
            LineRenderer lineRenderer = orbitObj.AddComponent<LineRenderer>();
            OrbitVisualizer visualizer = orbitObj.AddComponent<OrbitVisualizer>();
            
            visualizer.SetOrbitParameters(center, radius);
            
            return visualizer;
        }
        
        /// <summary>
        /// 在Scene视图中绘制轨道（仅编辑器）
        /// </summary>
        void OnDrawGizmosSelected()
        {
            if (orbitCenter == null) return;
            
            Gizmos.color = orbitColor;
            
            Vector3 centerPosition = orbitCenter.position;
            Vector3 previousPoint = centerPosition + Vector3.right * orbitRadius;
            
            for (int i = 1; i <= orbitSegments; i++)
            {
                float angle = (float)i / orbitSegments * 2f * Mathf.PI;
                float x = Mathf.Cos(angle) * orbitRadius;
                float z = Mathf.Sin(angle) * orbitRadius;
                
                Vector3 currentPoint = centerPosition + new Vector3(x, 0, z);
                Gizmos.DrawLine(previousPoint, currentPoint);
                previousPoint = currentPoint;
            }
        }
    }
}
