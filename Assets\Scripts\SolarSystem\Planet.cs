using UnityEngine;
using System.Collections.Generic;

namespace SolarSystem
{
    /// <summary>
    /// 行星类，包含行星特有的属性和行为
    /// </summary>
    public class Planet : CelestialBody
    {
        [Header("行星特有属性")]
        public bool hasRings = false;
        public GameObject ringsPrefab;
        public List<Planet> moons = new List<Planet>();
        
        [Header("大气层")]
        public GameObject atmospherePrefab;
        public float atmosphereScale = 1.2f;
        
        [Header("行星数据")]
        public float dayLength = 24f; // 地球小时
        public float yearLength = 365f; // 地球天
        public float axialTilt = 0f; // 轴倾斜角度
        public float eccentricity = 0f; // 轨道偏心率
        
        private GameObject ringsObject;
        private GameObject atmosphereObject;
        private List<GameObject> moonObjects = new List<GameObject>();
        
        protected override void Start()
        {
            base.Start();
            InitializePlanet();
        }
        
        protected override void Update()
        {
            base.Update();
            UpdateMoons();
        }
        
        /// <summary>
        /// 初始化行星特有组件
        /// </summary>
        private void InitializePlanet()
        {
            // 创建光环
            if (hasRings && ringsPrefab)
            {
                CreateRings();
            }
            
            // 创建大气层
            if (hasAtmosphere && atmospherePrefab)
            {
                CreateAtmosphere();
            }
            
            // 设置轴倾斜
            if (axialTilt != 0)
            {
                transform.rotation = Quaternion.Euler(0, 0, axialTilt);
            }
            
            // 初始化卫星
            InitializeMoons();
        }
        
        /// <summary>
        /// 创建光环
        /// </summary>
        private void CreateRings()
        {
            ringsObject = Instantiate(ringsPrefab, transform);
            ringsObject.name = bodyName + "_Rings";
            ringsObject.transform.localPosition = Vector3.zero;
            ringsObject.transform.localScale = Vector3.one * (radius * 2f);
        }
        
        /// <summary>
        /// 创建大气层
        /// </summary>
        private void CreateAtmosphere()
        {
            atmosphereObject = Instantiate(atmospherePrefab, transform);
            atmosphereObject.name = bodyName + "_Atmosphere";
            atmosphereObject.transform.localPosition = Vector3.zero;
            atmosphereObject.transform.localScale = Vector3.one * atmosphereScale;
            
            // 设置大气层颜色
            Renderer atmRenderer = atmosphereObject.GetComponent<Renderer>();
            if (atmRenderer)
            {
                atmRenderer.material.color = atmosphereColor;
            }
        }
        
        /// <summary>
        /// 初始化卫星
        /// </summary>
        private void InitializeMoons()
        {
            for (int i = 0; i < moons.Count; i++)
            {
                if (moons[i] != null)
                {
                    moons[i].SetOrbitCenter(transform);
                    // 为每个卫星设置不同的轨道距离
                    float moonDistance = radius * 3f + (i * radius * 2f);
                    moons[i].SetOrbitParameters(moonDistance, orbitSpeed * 2f);
                }
            }
        }
        
        /// <summary>
        /// 更新卫星位置
        /// </summary>
        private void UpdateMoons()
        {
            // 卫星会自动更新，这里可以添加额外的逻辑
        }
        
        /// <summary>
        /// 添加卫星
        /// </summary>
        public void AddMoon(Planet moon)
        {
            if (moon != null && !moons.Contains(moon))
            {
                moons.Add(moon);
                moon.SetOrbitCenter(transform);
                
                // 设置卫星轨道参数
                float moonDistance = radius * 3f + (moons.Count * radius * 2f);
                moon.SetOrbitParameters(moonDistance, orbitSpeed * 2f);
            }
        }
        
        /// <summary>
        /// 移除卫星
        /// </summary>
        public void RemoveMoon(Planet moon)
        {
            if (moons.Contains(moon))
            {
                moons.Remove(moon);
                moon.SetOrbitCenter(null);
            }
        }
        
        /// <summary>
        /// 设置行星数据
        /// </summary>
        public void SetPlanetData(float day, float year, float tilt, float ecc)
        {
            dayLength = day;
            yearLength = year;
            axialTilt = tilt;
            eccentricity = ecc;
            
            // 根据数据调整旋转速度
            rotationSpeed = 360f / (dayLength * 3600f); // 度/秒
            
            // 设置轴倾斜
            transform.rotation = Quaternion.Euler(0, 0, axialTilt);
        }
        
        /// <summary>
        /// 获取行星信息
        /// </summary>
        public string GetPlanetInfo()
        {
            return $"行星: {bodyName}\n" +
                   $"质量: {mass}\n" +
                   $"半径: {radius}\n" +
                   $"轨道距离: {orbitDistance}\n" +
                   $"一天长度: {dayLength}小时\n" +
                   $"一年长度: {yearLength}天\n" +
                   $"轴倾斜: {axialTilt}°\n" +
                   $"卫星数量: {moons.Count}";
        }
    }
}
