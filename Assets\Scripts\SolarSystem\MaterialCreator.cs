using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace SolarSystem
{
    /// <summary>
    /// 材质创建器，用于生成太阳系所需的各种材质
    /// </summary>
    public class MaterialCreator : MonoBehaviour
    {
        [Header("材质设置")]
        public bool createOnStart = false;
        public string materialFolder = "Assets/Materials/SolarSystem";
        
        [Header("太阳材质")]
        public Color sunColor = new Color(1f, 0.9f, 0.3f);
        public float sunEmissionIntensity = 2f;
        
        [Header("行星材质颜色")]
        public Color mercuryColor = new Color(0.7f, 0.7f, 0.7f);  // 水星 - 灰色
        public Color venusColor = new Color(1f, 0.8f, 0.4f);      // 金星 - 橙黄色
        public Color earthColor = new Color(0.2f, 0.6f, 1f);      // 地球 - 蓝色
        public Color marsColor = new Color(0.8f, 0.4f, 0.2f);     // 火星 - 红色
        public Color jupiterColor = new Color(0.9f, 0.7f, 0.4f);  // 木星 - 橙色
        public Color saturnColor = new Color(0.9f, 0.8f, 0.6f);   // 土星 - 淡黄色
        public Color uranusColor = new Color(0.4f, 0.8f, 0.9f);   // 天王星 - 青色
        public Color neptuneColor = new Color(0.2f, 0.4f, 0.8f);  // 海王星 - 蓝色
        
        [Header("材质属性")]
        [Range(0f, 1f)]
        public float planetMetallic = 0.1f;
        [Range(0f, 1f)]
        public float planetSmoothness = 0.3f;
        
        [Header("特殊材质")]
        public Color orbitLineColor = new Color(1f, 1f, 1f, 0.3f);
        public Color atmosphereColor = new Color(0.5f, 0.8f, 1f, 0.2f);
        
        void Start()
        {
            if (createOnStart)
            {
                CreateAllMaterials();
            }
        }
        
        /// <summary>
        /// 创建所有材质
        /// </summary>
        [ContextMenu("Create All Materials")]
        public void CreateAllMaterials()
        {
            #if UNITY_EDITOR
            // 确保文件夹存在
            CreateMaterialFolder();
            
            // 创建太阳材质
            CreateSunMaterial();
            
            // 创建行星材质
            CreatePlanetMaterials();
            
            // 创建特殊材质
            CreateSpecialMaterials();
            
            // 刷新资源
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            
            Debug.Log("所有太阳系材质创建完成！");
            #else
            Debug.LogWarning("材质创建功能仅在编辑器中可用！");
            #endif
        }
        
        #if UNITY_EDITOR
        /// <summary>
        /// 创建材质文件夹
        /// </summary>
        private void CreateMaterialFolder()
        {
            if (!AssetDatabase.IsValidFolder(materialFolder))
            {
                string[] folders = materialFolder.Split('/');
                string currentPath = folders[0];
                
                for (int i = 1; i < folders.Length; i++)
                {
                    string newPath = currentPath + "/" + folders[i];
                    if (!AssetDatabase.IsValidFolder(newPath))
                    {
                        AssetDatabase.CreateFolder(currentPath, folders[i]);
                    }
                    currentPath = newPath;
                }
            }
        }
        
        /// <summary>
        /// 创建太阳材质
        /// </summary>
        private void CreateSunMaterial()
        {
            Material sunMaterial = new Material(Shader.Find("Standard"));
            sunMaterial.name = "SunMaterial";
            
            // 设置基础颜色
            sunMaterial.SetColor("_Color", sunColor);
            
            // 设置发光
            sunMaterial.SetColor("_EmissionColor", sunColor * sunEmissionIntensity);
            sunMaterial.EnableKeyword("_EMISSION");
            sunMaterial.globalIlluminationFlags = MaterialGlobalIlluminationFlags.RealtimeEmissive;
            
            // 设置其他属性
            sunMaterial.SetFloat("_Metallic", 0f);
            sunMaterial.SetFloat("_Glossiness", 0.8f);
            
            // 保存材质
            string path = materialFolder + "/SunMaterial.mat";
            AssetDatabase.CreateAsset(sunMaterial, path);
            
            Debug.Log("太阳材质创建完成: " + path);
        }
        
        /// <summary>
        /// 创建行星材质
        /// </summary>
        private void CreatePlanetMaterials()
        {
            // 行星数据
            var planetData = new[]
            {
                new { name = "Mercury", color = mercuryColor, metallic = 0.2f, smoothness = 0.1f },
                new { name = "Venus", color = venusColor, metallic = 0.1f, smoothness = 0.9f },
                new { name = "Earth", color = earthColor, metallic = 0.0f, smoothness = 0.5f },
                new { name = "Mars", color = marsColor, metallic = 0.1f, smoothness = 0.2f },
                new { name = "Jupiter", color = jupiterColor, metallic = 0.0f, smoothness = 0.8f },
                new { name = "Saturn", color = saturnColor, metallic = 0.0f, smoothness = 0.7f },
                new { name = "Uranus", color = uranusColor, metallic = 0.0f, smoothness = 0.9f },
                new { name = "Neptune", color = neptuneColor, metallic = 0.0f, smoothness = 0.9f }
            };
            
            foreach (var planet in planetData)
            {
                Material planetMaterial = new Material(Shader.Find("Standard"));
                planetMaterial.name = planet.name + "Material";
                
                // 设置颜色
                planetMaterial.SetColor("_Color", planet.color);
                
                // 设置材质属性
                planetMaterial.SetFloat("_Metallic", planet.metallic);
                planetMaterial.SetFloat("_Glossiness", planet.smoothness);
                
                // 特殊设置
                if (planet.name == "Earth")
                {
                    // 地球可以有一些特殊的设置
                    planetMaterial.SetFloat("_BumpScale", 1f);
                }
                
                // 保存材质
                string path = materialFolder + "/" + planet.name + "Material.mat";
                AssetDatabase.CreateAsset(planetMaterial, path);
                
                Debug.Log($"{planet.name}材质创建完成: " + path);
            }
        }
        
        /// <summary>
        /// 创建特殊材质
        /// </summary>
        private void CreateSpecialMaterials()
        {
            // 轨道线材质
            CreateOrbitLineMaterial();
            
            // 大气层材质
            CreateAtmosphereMaterial();
            
            // 光环材质
            CreateRingMaterial();
        }
        
        /// <summary>
        /// 创建轨道线材质
        /// </summary>
        private void CreateOrbitLineMaterial()
        {
            Material orbitMaterial = new Material(Shader.Find("Sprites/Default"));
            orbitMaterial.name = "OrbitLineMaterial";
            
            orbitMaterial.SetColor("_Color", orbitLineColor);
            
            // 设置透明度
            orbitMaterial.SetFloat("_Mode", 3); // Transparent mode
            orbitMaterial.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            orbitMaterial.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            orbitMaterial.SetInt("_ZWrite", 0);
            orbitMaterial.DisableKeyword("_ALPHATEST_ON");
            orbitMaterial.EnableKeyword("_ALPHABLEND_ON");
            orbitMaterial.DisableKeyword("_ALPHAPREMULTIPLY_ON");
            orbitMaterial.renderQueue = 3000;
            
            string path = materialFolder + "/OrbitLineMaterial.mat";
            AssetDatabase.CreateAsset(orbitMaterial, path);
            
            Debug.Log("轨道线材质创建完成: " + path);
        }
        
        /// <summary>
        /// 创建大气层材质
        /// </summary>
        private void CreateAtmosphereMaterial()
        {
            Material atmosphereMaterial = new Material(Shader.Find("Standard"));
            atmosphereMaterial.name = "AtmosphereMaterial";
            
            atmosphereMaterial.SetColor("_Color", atmosphereColor);
            
            // 设置透明度
            atmosphereMaterial.SetFloat("_Mode", 3); // Transparent mode
            atmosphereMaterial.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            atmosphereMaterial.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            atmosphereMaterial.SetInt("_ZWrite", 0);
            atmosphereMaterial.DisableKeyword("_ALPHATEST_ON");
            atmosphereMaterial.EnableKeyword("_ALPHABLEND_ON");
            atmosphereMaterial.DisableKeyword("_ALPHAPREMULTIPLY_ON");
            atmosphereMaterial.renderQueue = 3000;
            
            // 设置其他属性
            atmosphereMaterial.SetFloat("_Metallic", 0f);
            atmosphereMaterial.SetFloat("_Glossiness", 0.9f);
            
            string path = materialFolder + "/AtmosphereMaterial.mat";
            AssetDatabase.CreateAsset(atmosphereMaterial, path);
            
            Debug.Log("大气层材质创建完成: " + path);
        }
        
        /// <summary>
        /// 创建光环材质
        /// </summary>
        private void CreateRingMaterial()
        {
            Material ringMaterial = new Material(Shader.Find("Standard"));
            ringMaterial.name = "RingMaterial";
            
            Color ringColor = new Color(0.8f, 0.7f, 0.6f, 0.5f);
            ringMaterial.SetColor("_Color", ringColor);
            
            // 设置透明度
            ringMaterial.SetFloat("_Mode", 3); // Transparent mode
            ringMaterial.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            ringMaterial.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            ringMaterial.SetInt("_ZWrite", 0);
            ringMaterial.DisableKeyword("_ALPHATEST_ON");
            ringMaterial.EnableKeyword("_ALPHABLEND_ON");
            ringMaterial.DisableKeyword("_ALPHAPREMULTIPLY_ON");
            ringMaterial.renderQueue = 3000;
            
            // 设置其他属性
            ringMaterial.SetFloat("_Metallic", 0.3f);
            ringMaterial.SetFloat("_Glossiness", 0.1f);
            
            string path = materialFolder + "/RingMaterial.mat";
            AssetDatabase.CreateAsset(ringMaterial, path);
            
            Debug.Log("光环材质创建完成: " + path);
        }
        #endif
        
        /// <summary>
        /// 获取材质路径
        /// </summary>
        public string GetMaterialPath(string materialName)
        {
            return materialFolder + "/" + materialName + ".mat";
        }
        
        /// <summary>
        /// 加载材质
        /// </summary>
        public Material LoadMaterial(string materialName)
        {
            #if UNITY_EDITOR
            string path = GetMaterialPath(materialName);
            return AssetDatabase.LoadAssetAtPath<Material>(path);
            #else
            return Resources.Load<Material>("Materials/SolarSystem/" + materialName);
            #endif
        }
        
        /// <summary>
        /// 应用材质到太阳系管理器
        /// </summary>
        [ContextMenu("Apply Materials to Solar System")]
        public void ApplyMaterialsToSolarSystem()
        {
            SolarSystemManager manager = FindObjectOfType<SolarSystemManager>();
            if (manager == null)
            {
                Debug.LogWarning("未找到太阳系管理器！");
                return;
            }
            
            // 应用太阳材质
            if (manager.sun)
            {
                Material sunMat = LoadMaterial("SunMaterial");
                if (sunMat && manager.sun.bodyRenderer)
                {
                    manager.sun.bodyRenderer.material = sunMat;
                }
            }
            
            // 应用行星材质
            string[] planetMaterialNames = { "Mercury", "Venus", "Earth", "Mars", "Jupiter", "Saturn", "Uranus", "Neptune" };
            
            for (int i = 0; i < manager.planets.Count && i < planetMaterialNames.Length; i++)
            {
                Planet planet = manager.planets[i];
                if (planet && planet.bodyRenderer)
                {
                    Material planetMat = LoadMaterial(planetMaterialNames[i] + "Material");
                    if (planetMat)
                    {
                        planet.bodyRenderer.material = planetMat;
                    }
                }
            }
            
            Debug.Log("材质已应用到太阳系！");
        }
    }
}
