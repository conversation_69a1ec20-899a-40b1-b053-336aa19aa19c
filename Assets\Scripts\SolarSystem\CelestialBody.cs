using UnityEngine;

namespace SolarSystem
{
    /// <summary>
    /// 天体基类，包含所有天体的基本属性和行为
    /// </summary>
    public class CelestialBody : MonoBehaviour
    {
        [Header("基本属性")]
        public string bodyName = "天体";
        public float mass = 1f;
        public float radius = 1f;
        
        [Header("轨道属性")]
        public float orbitDistance = 10f;
        public float orbitSpeed = 1f;
        public float rotationSpeed = 1f;
        public Transform orbitCenter;
        
        [Header("视觉属性")]
        public Material bodyMaterial;
        public bool hasAtmosphere = false;
        public Color atmosphereColor = Color.blue;
        
        protected float currentOrbitAngle = 0f;
        protected Vector3 initialPosition;
        protected Renderer bodyRenderer;
        
        protected virtual void Start()
        {
            InitializeBody();
        }
        
        protected virtual void Update()
        {
            UpdateOrbit();
            UpdateRotation();
        }
        
        /// <summary>
        /// 初始化天体
        /// </summary>
        protected virtual void InitializeBody()
        {
            bodyRenderer = GetComponent<Renderer>();
            if (bodyRenderer && bodyMaterial)
            {
                bodyRenderer.material = bodyMaterial;
            }
            
            // 设置初始位置
            if (orbitCenter)
            {
                initialPosition = orbitCenter.position + Vector3.right * orbitDistance;
                transform.position = initialPosition;
            }
            
            // 设置大小
            transform.localScale = Vector3.one * radius;
        }
        
        /// <summary>
        /// 更新轨道运动
        /// </summary>
        protected virtual void UpdateOrbit()
        {
            if (orbitCenter == null) return;
            
            currentOrbitAngle += orbitSpeed * Time.deltaTime;
            
            float x = Mathf.Cos(currentOrbitAngle) * orbitDistance;
            float z = Mathf.Sin(currentOrbitAngle) * orbitDistance;
            
            transform.position = orbitCenter.position + new Vector3(x, 0, z);
        }
        
        /// <summary>
        /// 更新自转
        /// </summary>
        protected virtual void UpdateRotation()
        {
            transform.Rotate(Vector3.up, rotationSpeed * Time.deltaTime);
        }
        
        /// <summary>
        /// 设置轨道中心
        /// </summary>
        public void SetOrbitCenter(Transform center)
        {
            orbitCenter = center;
            if (center)
            {
                initialPosition = center.position + Vector3.right * orbitDistance;
                transform.position = initialPosition;
            }
        }
        
        /// <summary>
        /// 获取当前轨道位置
        /// </summary>
        public Vector3 GetOrbitPosition()
        {
            return transform.position;
        }
        
        /// <summary>
        /// 设置轨道参数
        /// </summary>
        public void SetOrbitParameters(float distance, float speed)
        {
            orbitDistance = distance;
            orbitSpeed = speed;
        }
    }
}
