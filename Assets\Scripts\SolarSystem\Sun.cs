using UnityEngine;

namespace SolarSystem
{
    /// <summary>
    /// 太阳类，太阳系的中心天体
    /// </summary>
    public class Sun : CelestialBody
    {
        [Header("太阳特有属性")]
        public Light sunLight;
        public float lightIntensity = 2f;
        public Color sunColor = Color.yellow;
        public bool enableGlow = true;
        
        [Header("发光效果")]
        public Material glowMaterial;
        public float glowIntensity = 1f;
        public float pulseSpeed = 1f;
        public bool enablePulse = true;
        
        private float baseLightIntensity;
        private Color baseSunColor;
        
        protected override void Start()
        {
            // 太阳不需要轨道运动
            orbitSpeed = 0f;
            orbitDistance = 0f;
            
            base.Start();
            
            InitializeSun();
        }
        
        protected override void Update()
        {
            // 太阳只自转，不公转
            UpdateRotation();
            UpdateGlowEffect();
        }
        
        /// <summary>
        /// 初始化太阳特有属性
        /// </summary>
        private void InitializeSun()
        {
            // 设置太阳光源
            if (sunLight == null)
            {
                sunLight = GetComponent<Light>();
                if (sunLight == null)
                {
                    GameObject lightObj = new GameObject("SunLight");
                    lightObj.transform.SetParent(transform);
                    lightObj.transform.localPosition = Vector3.zero;
                    sunLight = lightObj.AddComponent<Light>();
                }
            }
            
            if (sunLight)
            {
                sunLight.type = LightType.Directional;
                sunLight.intensity = lightIntensity;
                sunLight.color = sunColor;
                baseLightIntensity = lightIntensity;
                baseSunColor = sunColor;
            }
            
            // 设置发光材质
            if (enableGlow && glowMaterial && bodyRenderer)
            {
                bodyRenderer.material = glowMaterial;
                bodyRenderer.material.SetColor("_EmissionColor", sunColor * glowIntensity);
                bodyRenderer.material.EnableKeyword("_EMISSION");
            }
        }
        
        /// <summary>
        /// 更新发光效果
        /// </summary>
        private void UpdateGlowEffect()
        {
            if (!enableGlow || !bodyRenderer || !enablePulse) return;
            
            float pulse = 1f + Mathf.Sin(Time.time * pulseSpeed) * 0.2f;
            
            if (sunLight)
            {
                sunLight.intensity = baseLightIntensity * pulse;
            }
            
            if (bodyRenderer.material.HasProperty("_EmissionColor"))
            {
                Color emissionColor = baseSunColor * glowIntensity * pulse;
                bodyRenderer.material.SetColor("_EmissionColor", emissionColor);
            }
        }
        
        /// <summary>
        /// 设置太阳光照强度
        /// </summary>
        public void SetLightIntensity(float intensity)
        {
            lightIntensity = intensity;
            baseLightIntensity = intensity;
            if (sunLight)
            {
                sunLight.intensity = intensity;
            }
        }
        
        /// <summary>
        /// 设置太阳颜色
        /// </summary>
        public void SetSunColor(Color color)
        {
            sunColor = color;
            baseSunColor = color;
            if (sunLight)
            {
                sunLight.color = color;
            }
            
            if (bodyRenderer && bodyRenderer.material.HasProperty("_EmissionColor"))
            {
                bodyRenderer.material.SetColor("_EmissionColor", color * glowIntensity);
            }
        }
        
        /// <summary>
        /// 启用/禁用脉冲效果
        /// </summary>
        public void SetPulseEnabled(bool enabled)
        {
            enablePulse = enabled;
            if (!enabled && sunLight)
            {
                sunLight.intensity = baseLightIntensity;
            }
        }
    }
}
