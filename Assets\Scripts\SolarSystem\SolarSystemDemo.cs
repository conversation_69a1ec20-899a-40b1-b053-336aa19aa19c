using UnityEngine;
using System.Collections;

namespace SolarSystem
{
    /// <summary>
    /// 太阳系演示脚本，展示各种功能和效果
    /// </summary>
    public class SolarSystemDemo : MonoBehaviour
    {
        [Header("演示设置")]
        public bool autoStartDemo = true;
        public float demoStepDuration = 5f;
        public bool loopDemo = true;
        
        [Header("引用组件")]
        public SolarSystemManager solarSystemManager;
        public SolarSystemCamera solarSystemCamera;
        public SolarSystemUI solarSystemUI;
        
        [Header("演示步骤")]
        public bool showOverview = true;
        public bool showPlanetTour = true;
        public bool showTimeControl = true;
        public bool showCameraModes = true;
        public bool showOrbitVisualization = true;
        
        private int currentDemoStep = 0;
        private bool isDemoRunning = false;
        private Coroutine demoCoroutine;
        
        // 演示步骤枚举
        private enum DemoStep
        {
            Overview,
            PlanetTour,
            TimeControl,
            CameraModes,
            OrbitVisualization,
            Complete
        }
        
        void Start()
        {
            InitializeDemo();
            
            if (autoStartDemo)
            {
                StartDemo();
            }
        }
        
        void Update()
        {
            HandleDemoInput();
        }
        
        /// <summary>
        /// 初始化演示
        /// </summary>
        private void InitializeDemo()
        {
            // 查找组件
            if (solarSystemManager == null)
                solarSystemManager = FindObjectOfType<SolarSystemManager>();
            
            if (solarSystemCamera == null)
                solarSystemCamera = FindObjectOfType<SolarSystemCamera>();
            
            if (solarSystemUI == null)
                solarSystemUI = FindObjectOfType<SolarSystemUI>();
            
            // 设置初始状态
            if (solarSystemManager)
            {
                solarSystemManager.timeScale = 1f;
                solarSystemManager.isPaused = false;
            }
        }
        
        /// <summary>
        /// 处理演示输入
        /// </summary>
        private void HandleDemoInput()
        {
            // F5 开始/停止演示
            if (Input.GetKeyDown(KeyCode.F5))
            {
                if (isDemoRunning)
                {
                    StopDemo();
                }
                else
                {
                    StartDemo();
                }
            }
            
            // F6 下一步
            if (Input.GetKeyDown(KeyCode.F6))
            {
                NextDemoStep();
            }
            
            // F7 重置演示
            if (Input.GetKeyDown(KeyCode.F7))
            {
                ResetDemo();
            }
        }
        
        /// <summary>
        /// 开始演示
        /// </summary>
        public void StartDemo()
        {
            if (isDemoRunning) return;
            
            isDemoRunning = true;
            currentDemoStep = 0;
            
            demoCoroutine = StartCoroutine(RunDemo());
            
            Debug.Log("太阳系演示开始！");
            ShowDemoMessage("太阳系演示开始！\n按F5停止，F6下一步，F7重置");
        }
        
        /// <summary>
        /// 停止演示
        /// </summary>
        public void StopDemo()
        {
            if (!isDemoRunning) return;
            
            isDemoRunning = false;
            
            if (demoCoroutine != null)
            {
                StopCoroutine(demoCoroutine);
                demoCoroutine = null;
            }
            
            Debug.Log("太阳系演示停止！");
            ShowDemoMessage("演示已停止");
        }
        
        /// <summary>
        /// 重置演示
        /// </summary>
        public void ResetDemo()
        {
            StopDemo();
            
            currentDemoStep = 0;
            
            // 重置太阳系状态
            if (solarSystemManager)
            {
                solarSystemManager.ResetSolarSystem();
            }
            
            // 重置相机
            if (solarSystemCamera)
            {
                solarSystemCamera.SetOverviewMode();
            }
            
            Debug.Log("演示已重置！");
            ShowDemoMessage("演示已重置");
        }
        
        /// <summary>
        /// 下一步演示
        /// </summary>
        public void NextDemoStep()
        {
            if (!isDemoRunning) return;
            
            currentDemoStep++;
            ExecuteDemoStep((DemoStep)currentDemoStep);
        }
        
        /// <summary>
        /// 运行演示
        /// </summary>
        private IEnumerator RunDemo()
        {
            while (isDemoRunning)
            {
                ExecuteDemoStep((DemoStep)currentDemoStep);
                
                yield return new WaitForSeconds(demoStepDuration);
                
                currentDemoStep++;
                
                if (currentDemoStep >= System.Enum.GetValues(typeof(DemoStep)).Length)
                {
                    if (loopDemo)
                    {
                        currentDemoStep = 0;
                        ShowDemoMessage("演示循环重新开始");
                    }
                    else
                    {
                        isDemoRunning = false;
                        ShowDemoMessage("演示完成！");
                        break;
                    }
                }
            }
        }
        
        /// <summary>
        /// 执行演示步骤
        /// </summary>
        private void ExecuteDemoStep(DemoStep step)
        {
            switch (step)
            {
                case DemoStep.Overview:
                    if (showOverview) DemoOverview();
                    break;
                    
                case DemoStep.PlanetTour:
                    if (showPlanetTour) StartCoroutine(DemoPlanetTour());
                    break;
                    
                case DemoStep.TimeControl:
                    if (showTimeControl) StartCoroutine(DemoTimeControl());
                    break;
                    
                case DemoStep.CameraModes:
                    if (showCameraModes) StartCoroutine(DemoCameraModes());
                    break;
                    
                case DemoStep.OrbitVisualization:
                    if (showOrbitVisualization) DemoOrbitVisualization();
                    break;
                    
                case DemoStep.Complete:
                    DemoComplete();
                    break;
            }
        }
        
        /// <summary>
        /// 演示总览
        /// </summary>
        private void DemoOverview()
        {
            if (solarSystemCamera)
            {
                solarSystemCamera.SetOverviewMode();
            }
            
            ShowDemoMessage("太阳系总览\n这是我们的太阳系，包含太阳和八大行星");
        }
        
        /// <summary>
        /// 演示行星巡游
        /// </summary>
        private IEnumerator DemoPlanetTour()
        {
            ShowDemoMessage("行星巡游开始");
            
            if (solarSystemManager && solarSystemCamera)
            {
                // 先访问太阳
                if (solarSystemManager.sun)
                {
                    solarSystemCamera.SetFollowTarget(solarSystemManager.sun.transform);
                    solarSystemCamera.SetFollowMode();
                    ShowDemoMessage("太阳 - 太阳系的中心恒星");
                    yield return new WaitForSeconds(2f);
                }
                
                // 依次访问每个行星
                for (int i = 0; i < solarSystemManager.planets.Count; i++)
                {
                    Planet planet = solarSystemManager.planets[i];
                    if (planet)
                    {
                        solarSystemCamera.SetFollowTarget(planet.transform);
                        solarSystemCamera.SetFollowMode();
                        ShowDemoMessage($"{planet.bodyName}\n{planet.GetPlanetInfo()}");
                        yield return new WaitForSeconds(1.5f);
                    }
                }
            }
        }
        
        /// <summary>
        /// 演示时间控制
        /// </summary>
        private IEnumerator DemoTimeControl()
        {
            ShowDemoMessage("时间控制演示");
            
            if (solarSystemManager)
            {
                // 正常速度
                solarSystemManager.SetTimeScale(1f);
                ShowDemoMessage("正常时间速度 (1x)");
                yield return new WaitForSeconds(1f);
                
                // 加速
                solarSystemManager.SetTimeScale(5f);
                ShowDemoMessage("时间加速 (5x)");
                yield return new WaitForSeconds(1f);
                
                // 超高速
                solarSystemManager.SetTimeScale(10f);
                ShowDemoMessage("超高速 (10x)");
                yield return new WaitForSeconds(1f);
                
                // 暂停
                solarSystemManager.isPaused = true;
                ShowDemoMessage("时间暂停");
                yield return new WaitForSeconds(1f);
                
                // 恢复
                solarSystemManager.isPaused = false;
                solarSystemManager.SetTimeScale(1f);
                ShowDemoMessage("恢复正常速度");
            }
        }
        
        /// <summary>
        /// 演示相机模式
        /// </summary>
        private IEnumerator DemoCameraModes()
        {
            ShowDemoMessage("相机模式演示");
            
            if (solarSystemCamera)
            {
                // 自由模式
                solarSystemCamera.SetFreeMode();
                ShowDemoMessage("自由模式 - 使用WASD和鼠标控制");
                yield return new WaitForSeconds(1.5f);
                
                // 跟随模式
                solarSystemCamera.SetFollowMode();
                ShowDemoMessage("跟随模式 - 跟随选定的天体");
                yield return new WaitForSeconds(1.5f);
                
                // 轨道模式
                solarSystemCamera.SetOrbitMode();
                ShowDemoMessage("轨道模式 - 围绕天体旋转");
                yield return new WaitForSeconds(1.5f);
                
                // 总览模式
                solarSystemCamera.SetOverviewMode();
                ShowDemoMessage("总览模式 - 俯视整个太阳系");
            }
        }
        
        /// <summary>
        /// 演示轨道可视化
        /// </summary>
        private void DemoOrbitVisualization()
        {
            ShowDemoMessage("轨道可视化\n显示行星运行轨迹");
            
            // 这里可以添加轨道显示/隐藏的代码
            // 需要配合OrbitVisualizer组件使用
        }
        
        /// <summary>
        /// 演示完成
        /// </summary>
        private void DemoComplete()
        {
            ShowDemoMessage("演示完成！\n感谢观看太阳系模拟器演示");
            
            if (solarSystemCamera)
            {
                solarSystemCamera.SetOverviewMode();
            }
        }
        
        /// <summary>
        /// 显示演示消息
        /// </summary>
        private void ShowDemoMessage(string message)
        {
            Debug.Log($"[演示] {message}");
            
            // 如果有UI组件，可以在UI上显示消息
            if (solarSystemUI && solarSystemUI.planetInfoText)
            {
                solarSystemUI.planetInfoText.text = message;
            }
        }
        
        /// <summary>
        /// 获取演示状态
        /// </summary>
        public bool IsDemoRunning()
        {
            return isDemoRunning;
        }
        
        /// <summary>
        /// 获取当前演示步骤
        /// </summary>
        public int GetCurrentStep()
        {
            return currentDemoStep;
        }
        
        /// <summary>
        /// 设置演示步骤持续时间
        /// </summary>
        public void SetDemoStepDuration(float duration)
        {
            demoStepDuration = Mathf.Max(1f, duration);
        }
    }
}
