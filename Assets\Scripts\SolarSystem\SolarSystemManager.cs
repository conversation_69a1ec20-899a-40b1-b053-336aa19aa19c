using UnityEngine;
using System.Collections.Generic;

namespace SolarSystem
{
    /// <summary>
    /// 太阳系管理器，控制整个太阳系的运行
    /// </summary>
    public class SolarSystemManager : MonoBehaviour
    {
        [Header("太阳系设置")]
        public Sun sun;
        public List<Planet> planets = new List<Planet>();
        
        [Header("时间控制")]
        public float timeScale = 1f;
        public bool isPaused = false;
        
        [Header("预制体")]
        public GameObject sunPrefab;
        public GameObject planetPrefab;
        public GameObject moonPrefab;
        
        [Header("材质")]
        public Material sunMaterial;
        public Material[] planetMaterials;
        
        [Header("真实太阳系数据")]
        public bool useRealisticData = true;
        public float scaleFactor = 0.1f; // 缩放因子
        
        // 真实行星数据
        private readonly PlanetData[] realPlanetData = new PlanetData[]
        {
            new PlanetData("水星", 0.39f, 0.24f, 58.6f, 0f, 0.38f),
            new PlanetData("金星", 0.72f, 0.62f, -243f, 177f, 0.95f),
            new PlanetData("地球", 1f, 1f, 1f, 23.4f, 1f),
            new PlanetData("火星", 1.52f, 1.88f, 1.03f, 25.2f, 0.53f),
            new PlanetData("木星", 5.2f, 11.86f, 0.41f, 3.1f, 11.2f),
            new PlanetData("土星", 9.5f, 29.46f, 0.45f, 26.7f, 9.4f),
            new PlanetData("天王星", 19.2f, 84.01f, -0.72f, 97.8f, 4f),
            new PlanetData("海王星", 30.1f, 164.8f, 0.67f, 28.3f, 3.9f)
        };
        
        private float originalTimeScale;
        
        void Start()
        {
            originalTimeScale = Time.timeScale;
            InitializeSolarSystem();
        }
        
        void Update()
        {
            UpdateTimeScale();
            HandleInput();
        }
        
        /// <summary>
        /// 初始化太阳系
        /// </summary>
        private void InitializeSolarSystem()
        {
            if (useRealisticData)
            {
                CreateRealisticSolarSystem();
            }
            else
            {
                CreateSimpleSolarSystem();
            }
        }
        
        /// <summary>
        /// 创建真实太阳系
        /// </summary>
        private void CreateRealisticSolarSystem()
        {
            // 创建太阳
            if (sun == null && sunPrefab)
            {
                GameObject sunObj = Instantiate(sunPrefab, Vector3.zero, Quaternion.identity);
                sun = sunObj.GetComponent<Sun>();
                if (sun == null) sun = sunObj.AddComponent<Sun>();
                
                sun.bodyName = "太阳";
                sun.radius = 1f;
                sun.bodyMaterial = sunMaterial;
            }
            
            // 创建行星
            planets.Clear();
            for (int i = 0; i < realPlanetData.Length; i++)
            {
                CreatePlanet(realPlanetData[i], i);
            }
        }
        
        /// <summary>
        /// 创建简单太阳系
        /// </summary>
        private void CreateSimpleSolarSystem()
        {
            // 创建太阳
            if (sun == null && sunPrefab)
            {
                GameObject sunObj = Instantiate(sunPrefab, Vector3.zero, Quaternion.identity);
                sun = sunObj.GetComponent<Sun>();
                if (sun == null) sun = sunObj.AddComponent<Sun>();
                
                sun.bodyName = "太阳";
                sun.radius = 2f;
            }
            
            // 创建几个简单的行星
            string[] planetNames = { "行星A", "行星B", "行星C", "行星D" };
            float[] distances = { 5f, 8f, 12f, 16f };
            float[] speeds = { 2f, 1.5f, 1f, 0.8f };
            float[] sizes = { 0.5f, 0.8f, 1f, 0.7f };
            
            for (int i = 0; i < planetNames.Length; i++)
            {
                CreateSimplePlanet(planetNames[i], distances[i], speeds[i], sizes[i], i);
            }
        }
        
        /// <summary>
        /// 创建行星
        /// </summary>
        private void CreatePlanet(PlanetData data, int index)
        {
            if (planetPrefab == null) return;
            
            GameObject planetObj = Instantiate(planetPrefab, Vector3.zero, Quaternion.identity);
            Planet planet = planetObj.GetComponent<Planet>();
            if (planet == null) planet = planetObj.AddComponent<Planet>();
            
            // 设置行星属性
            planet.bodyName = data.name;
            planet.orbitDistance = data.orbitDistance * 10f * scaleFactor;
            planet.orbitSpeed = (1f / data.orbitalPeriod) * timeScale;
            planet.rotationSpeed = (360f / (data.rotationPeriod * 24f)) * timeScale;
            planet.radius = data.radius * scaleFactor;
            planet.SetOrbitCenter(sun.transform);
            
            // 设置材质
            if (planetMaterials != null && index < planetMaterials.Length)
            {
                planet.bodyMaterial = planetMaterials[index];
            }
            
            // 设置行星数据
            planet.SetPlanetData(data.rotationPeriod * 24f, data.orbitalPeriod * 365f, data.axialTilt, 0f);
            
            planets.Add(planet);
        }
        
        /// <summary>
        /// 创建简单行星
        /// </summary>
        private void CreateSimplePlanet(string name, float distance, float speed, float size, int index)
        {
            if (planetPrefab == null) return;
            
            GameObject planetObj = Instantiate(planetPrefab, Vector3.zero, Quaternion.identity);
            Planet planet = planetObj.GetComponent<Planet>();
            if (planet == null) planet = planetObj.AddComponent<Planet>();
            
            planet.bodyName = name;
            planet.orbitDistance = distance;
            planet.orbitSpeed = speed;
            planet.rotationSpeed = 50f;
            planet.radius = size;
            planet.SetOrbitCenter(sun.transform);
            
            if (planetMaterials != null && index < planetMaterials.Length)
            {
                planet.bodyMaterial = planetMaterials[index];
            }
            
            planets.Add(planet);
        }
        
        /// <summary>
        /// 更新时间缩放
        /// </summary>
        private void UpdateTimeScale()
        {
            if (!isPaused)
            {
                Time.timeScale = timeScale;
            }
            else
            {
                Time.timeScale = 0f;
            }
        }
        
        /// <summary>
        /// 处理输入
        /// </summary>
        private void HandleInput()
        {
            // 空格键暂停/继续
            if (Input.GetKeyDown(KeyCode.Space))
            {
                TogglePause();
            }
            
            // 数字键调整时间速度
            if (Input.GetKeyDown(KeyCode.Alpha1)) timeScale = 0.1f;
            if (Input.GetKeyDown(KeyCode.Alpha2)) timeScale = 0.5f;
            if (Input.GetKeyDown(KeyCode.Alpha3)) timeScale = 1f;
            if (Input.GetKeyDown(KeyCode.Alpha4)) timeScale = 2f;
            if (Input.GetKeyDown(KeyCode.Alpha5)) timeScale = 5f;
            if (Input.GetKeyDown(KeyCode.Alpha6)) timeScale = 10f;
        }
        
        /// <summary>
        /// 切换暂停状态
        /// </summary>
        public void TogglePause()
        {
            isPaused = !isPaused;
        }
        
        /// <summary>
        /// 设置时间缩放
        /// </summary>
        public void SetTimeScale(float scale)
        {
            timeScale = Mathf.Max(0.1f, scale);
        }
        
        /// <summary>
        /// 重置太阳系
        /// </summary>
        public void ResetSolarSystem()
        {
            timeScale = 1f;
            isPaused = false;
            
            // 重新初始化所有天体位置
            foreach (Planet planet in planets)
            {
                if (planet != null)
                {
                    planet.SetOrbitCenter(sun.transform);
                }
            }
        }
    }
    
    /// <summary>
    /// 行星数据结构
    /// </summary>
    [System.Serializable]
    public struct PlanetData
    {
        public string name;
        public float orbitDistance; // AU
        public float orbitalPeriod; // 地球年
        public float rotationPeriod; // 地球天
        public float axialTilt; // 度
        public float radius; // 地球半径

        public PlanetData(string n, float od, float op, float rp, float at, float r)
        {
            name = n;
            orbitDistance = od;
            orbitalPeriod = op;
            rotationPeriod = rp;
            axialTilt = at;
            radius = r;
        }
    }
}
