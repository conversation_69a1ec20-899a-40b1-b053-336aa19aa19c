using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace SolarSystem
{
    /// <summary>
    /// 太阳系UI控制器
    /// </summary>
    public class SolarSystemUI : MonoBehaviour
    {
        [Header("UI面板")]
        public GameObject controlPanel;
        public GameObject infoPanel;
        public bool showUI = true;
        
        [Header("控制组件")]
        public Slider timeScaleSlider;
        public TextMeshProUGUI timeScaleText;
        public Button pauseButton;
        public Button resetButton;
        
        [Header("信息显示")]
        public TextMeshProUGUI planetInfoText;
        public TextMeshProUGUI instructionsText;
        
        [Header("相机控制")]
        public Dropdown cameraModeDropdown;
        public Dropdown targetDropdown;
        public Toggle autoRotateToggle;
        
        [Header("引用")]
        public SolarSystemManager solarSystemManager;
        public SolarSystemCamera solarSystemCamera;
        
        private bool isPanelVisible = true;
        
        void Start()
        {
            InitializeUI();
            UpdateInstructions();
        }
        
        void Update()
        {
            UpdateUI();
            HandleUIInput();
        }
        
        /// <summary>
        /// 初始化UI
        /// </summary>
        private void InitializeUI()
        {
            // 时间缩放滑块
            if (timeScaleSlider)
            {
                timeScaleSlider.minValue = 0.1f;
                timeScaleSlider.maxValue = 10f;
                timeScaleSlider.value = 1f;
                timeScaleSlider.onValueChanged.AddListener(OnTimeScaleChanged);
            }
            
            // 暂停按钮
            if (pauseButton)
            {
                pauseButton.onClick.AddListener(OnPauseClicked);
            }
            
            // 重置按钮
            if (resetButton)
            {
                resetButton.onClick.AddListener(OnResetClicked);
            }
            
            // 相机模式下拉菜单
            if (cameraModeDropdown)
            {
                cameraModeDropdown.ClearOptions();
                cameraModeDropdown.AddOptions(new System.Collections.Generic.List<string>
                {
                    "自由模式", "跟随模式", "轨道模式", "总览模式"
                });
                cameraModeDropdown.onValueChanged.AddListener(OnCameraModeChanged);
            }
            
            // 目标下拉菜单
            if (targetDropdown && solarSystemManager)
            {
                UpdateTargetDropdown();
                targetDropdown.onValueChanged.AddListener(OnTargetChanged);
            }
            
            // 自动旋转开关
            if (autoRotateToggle)
            {
                autoRotateToggle.onValueChanged.AddListener(OnAutoRotateChanged);
            }
            
            // 设置面板可见性
            SetPanelVisibility(showUI);
        }
        
        /// <summary>
        /// 更新UI
        /// </summary>
        private void UpdateUI()
        {
            // 更新时间缩放显示
            if (timeScaleText && solarSystemManager)
            {
                timeScaleText.text = $"时间速度: {solarSystemManager.timeScale:F1}x";
            }
            
            // 更新暂停按钮文本
            if (pauseButton && solarSystemManager)
            {
                TextMeshProUGUI buttonText = pauseButton.GetComponentInChildren<TextMeshProUGUI>();
                if (buttonText)
                {
                    buttonText.text = solarSystemManager.isPaused ? "继续" : "暂停";
                }
            }
            
            // 更新行星信息
            UpdatePlanetInfo();
        }
        
        /// <summary>
        /// 处理UI输入
        /// </summary>
        private void HandleUIInput()
        {
            // H键切换UI显示
            if (Input.GetKeyDown(KeyCode.H))
            {
                ToggleUI();
            }
            
            // ESC键显示/隐藏控制面板
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                ToggleControlPanel();
            }
        }
        
        /// <summary>
        /// 时间缩放改变
        /// </summary>
        private void OnTimeScaleChanged(float value)
        {
            if (solarSystemManager)
            {
                solarSystemManager.SetTimeScale(value);
            }
        }
        
        /// <summary>
        /// 暂停按钮点击
        /// </summary>
        private void OnPauseClicked()
        {
            if (solarSystemManager)
            {
                solarSystemManager.TogglePause();
            }
        }
        
        /// <summary>
        /// 重置按钮点击
        /// </summary>
        private void OnResetClicked()
        {
            if (solarSystemManager)
            {
                solarSystemManager.ResetSolarSystem();
            }
            
            if (timeScaleSlider)
            {
                timeScaleSlider.value = 1f;
            }
        }
        
        /// <summary>
        /// 相机模式改变
        /// </summary>
        private void OnCameraModeChanged(int index)
        {
            if (solarSystemCamera == null) return;
            
            switch (index)
            {
                case 0: solarSystemCamera.SetFreeMode(); break;
                case 1: solarSystemCamera.SetFollowMode(); break;
                case 2: solarSystemCamera.SetOrbitMode(); break;
                case 3: solarSystemCamera.SetOverviewMode(); break;
            }
        }
        
        /// <summary>
        /// 目标改变
        /// </summary>
        private void OnTargetChanged(int index)
        {
            if (solarSystemCamera == null || solarSystemManager == null) return;
            
            if (index == 0 && solarSystemManager.sun)
            {
                solarSystemCamera.SetFollowTarget(solarSystemManager.sun.transform);
            }
            else if (index > 0 && index - 1 < solarSystemManager.planets.Count)
            {
                solarSystemCamera.SetFollowTarget(solarSystemManager.planets[index - 1].transform);
            }
        }
        
        /// <summary>
        /// 自动旋转改变
        /// </summary>
        private void OnAutoRotateChanged(bool value)
        {
            if (solarSystemCamera)
            {
                solarSystemCamera.autoRotate = value;
            }
        }
        
        /// <summary>
        /// 更新目标下拉菜单
        /// </summary>
        private void UpdateTargetDropdown()
        {
            if (targetDropdown == null || solarSystemManager == null) return;
            
            targetDropdown.ClearOptions();
            var options = new System.Collections.Generic.List<string> { "太阳" };
            
            foreach (Planet planet in solarSystemManager.planets)
            {
                if (planet != null)
                {
                    options.Add(planet.bodyName);
                }
            }
            
            targetDropdown.AddOptions(options);
        }
        
        /// <summary>
        /// 更新行星信息
        /// </summary>
        private void UpdatePlanetInfo()
        {
            if (planetInfoText == null || solarSystemCamera == null) return;
            
            if (solarSystemCamera.followTarget != null)
            {
                Planet planet = solarSystemCamera.followTarget.GetComponent<Planet>();
                if (planet != null)
                {
                    planetInfoText.text = planet.GetPlanetInfo();
                }
                else
                {
                    Sun sun = solarSystemCamera.followTarget.GetComponent<Sun>();
                    if (sun != null)
                    {
                        planetInfoText.text = $"恒星: {sun.bodyName}\n半径: {sun.radius}\n类型: G型主序星";
                    }
                }
            }
            else
            {
                planetInfoText.text = "选择一个天体查看详细信息";
            }
        }
        
        /// <summary>
        /// 更新操作说明
        /// </summary>
        private void UpdateInstructions()
        {
            if (instructionsText)
            {
                instructionsText.text = 
                    "操作说明:\n" +
                    "WASD - 移动相机\n" +
                    "鼠标拖拽 - 旋转视角\n" +
                    "滚轮 - 缩放\n" +
                    "空格 - 暂停/继续\n" +
                    "Tab - 切换目标\n" +
                    "R - 自动旋转\n" +
                    "F1-F4 - 相机模式\n" +
                    "H - 显示/隐藏UI\n" +
                    "1-6 - 时间速度";
            }
        }
        
        /// <summary>
        /// 切换UI显示
        /// </summary>
        public void ToggleUI()
        {
            showUI = !showUI;
            SetPanelVisibility(showUI);
        }
        
        /// <summary>
        /// 切换控制面板
        /// </summary>
        public void ToggleControlPanel()
        {
            isPanelVisible = !isPanelVisible;
            if (controlPanel)
            {
                controlPanel.SetActive(isPanelVisible);
            }
        }
        
        /// <summary>
        /// 设置面板可见性
        /// </summary>
        private void SetPanelVisibility(bool visible)
        {
            if (controlPanel) controlPanel.SetActive(visible && isPanelVisible);
            if (infoPanel) infoPanel.SetActive(visible);
        }
    }
}
