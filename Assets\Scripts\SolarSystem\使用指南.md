# Unity 太阳系模拟器 - 使用指南

## 🚀 快速开始

### 方法一：自动设置（推荐）
1. 在Unity中打开场景
2. 创建一个空的GameObject，命名为"SolarSystemSetup"
3. 添加 `SolarSystemSetup` 脚本
4. 在Inspector中勾选 `Auto Setup On Start`
5. 点击Play按钮，系统将自动创建完整的太阳系

### 方法二：使用编辑器工具
1. 在Unity菜单栏选择 `Tools > Solar System > Solar System Editor`
2. 在打开的编辑器窗口中点击"创建完整太阳系"
3. 系统会自动创建所有必要的组件和资源

### 方法三：手动设置
1. 创建SolarSystemManager GameObject
2. 创建相机控制器
3. 设置UI系统
4. 配置材质和预制体

## 🎮 操作控制

### 基础控制
- **WASD** - 相机移动（自由模式）
- **鼠标拖拽** - 旋转视角
- **滚轮** - 缩放视野
- **空格键** - 暂停/继续模拟

### 高级控制
- **Tab键** - 切换跟随目标
- **R键** - 开启/关闭自动旋转
- **H键** - 显示/隐藏UI界面
- **ESC键** - 显示/隐藏控制面板

### 相机模式切换
- **F1** - 自由模式
- **F2** - 跟随模式
- **F3** - 轨道模式
- **F4** - 总览模式

### 时间控制
- **数字键1** - 0.1x速度
- **数字键2** - 0.5x速度
- **数字键3** - 1x速度（正常）
- **数字键4** - 2x速度
- **数字键5** - 5x速度
- **数字键6** - 10x速度

### 演示功能
- **F5** - 开始/停止自动演示
- **F6** - 下一个演示步骤
- **F7** - 重置演示

## 🛠️ 组件说明

### 核心组件

#### SolarSystemManager
太阳系的主控制器，负责：
- 创建和管理所有天体
- 控制时间缩放
- 处理暂停/继续
- 管理真实数据模式

**重要参数：**
- `useRealisticData` - 是否使用真实天体数据
- `scaleFactor` - 整体缩放因子
- `timeScale` - 时间速度倍数

#### SolarSystemCamera
相机控制系统，提供：
- 多种观察模式
- 平滑的相机移动
- 目标跟随功能
- 自动旋转

**重要参数：**
- `moveSpeed` - 移动速度
- `rotateSpeed` - 旋转速度
- `followDistance` - 跟随距离

#### CelestialBody
天体基类，包含：
- 轨道运动逻辑
- 自转功能
- 材质管理
- 基础属性

### 专用组件

#### Sun
太阳专用组件，特性：
- 发光效果
- 脉冲动画
- 光源管理
- 颜色控制

#### Planet
行星专用组件，支持：
- 卫星系统
- 大气层效果
- 光环系统
- 详细信息显示

## 🎨 自定义设置

### 修改行星数据
在 `SolarSystemManager.cs` 中找到 `realPlanetData` 数组：

```csharp
new PlanetData("行星名", 轨道距离AU, 公转周期年, 自转周期天, 轴倾斜度, 半径比例)
```

### 创建自定义材质
1. 在Project窗口右键 > Create > Material
2. 选择合适的Shader（Standard推荐）
3. 调整颜色、金属度、光滑度等参数
4. 将材质拖拽到对应的天体上

### 添加新天体
1. 复制现有的行星GameObject
2. 修改CelestialBody组件的参数
3. 在SolarSystemManager中添加到planets列表
4. 设置合适的轨道参数

## 🔧 高级功能

### 轨道可视化
使用 `OrbitVisualizer` 组件：
1. 添加到天体GameObject上
2. 设置轨道中心和半径
3. 调整颜色和线宽
4. 启用动画效果

### 自定义UI
修改 `SolarSystemUI.cs`：
- 添加新的控制按钮
- 创建信息显示面板
- 实现交互功能
- 自定义样式

### 性能优化
- 调整 `scaleFactor` 减少渲染负担
- 使用LOD系统优化远距离渲染
- 限制同时显示的天体数量
- 优化材质和纹理

## 📱 平台适配

### PC平台
- 完整功能支持
- 键盘鼠标控制
- 高质量渲染

### 移动平台
- 触摸控制适配
- 性能优化设置
- 简化UI界面

### VR平台
- 沉浸式体验
- 手柄控制
- 空间定位

## 🐛 常见问题

### 天体不运动
**解决方案：**
1. 检查时间缩放是否为0
2. 确认没有暂停模拟
3. 验证轨道中心设置正确

### 相机控制失效
**解决方案：**
1. 确认SolarSystemCamera脚本已添加
2. 检查相机模式设置
3. 验证输入系统正常

### UI不显示
**解决方案：**
1. 检查Canvas设置
2. 确认UI脚本已添加
3. 验证UI对象激活状态

### 材质显示异常
**解决方案：**
1. 检查材质Shader兼容性
2. 确认纹理导入设置
3. 验证光照设置

## 📚 学习资源

### Unity官方文档
- Transform组件
- LineRenderer组件
- 材质和Shader
- UI系统

### 天文学知识
- 太阳系基础知识
- 行星轨道力学
- 天体物理学

### 编程技巧
- 面向对象设计
- 组件化架构
- 性能优化
- 用户界面设计

## 🤝 贡献指南

### 报告问题
1. 详细描述问题现象
2. 提供复现步骤
3. 附上错误日志
4. 说明Unity版本

### 功能建议
1. 描述期望功能
2. 说明使用场景
3. 提供设计思路
4. 考虑实现难度

### 代码贡献
1. 遵循代码规范
2. 添加注释说明
3. 编写测试用例
4. 更新文档

---

希望这个太阳系模拟器能为您的学习和项目带来帮助！🌟
