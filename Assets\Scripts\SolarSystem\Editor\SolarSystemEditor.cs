#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;

namespace SolarSystem
{
    /// <summary>
    /// 太阳系编辑器工具
    /// </summary>
    public class SolarSystemEditor : EditorWindow
    {
        private SolarSystemManager solarSystemManager;
        private SolarSystemSetup setupTool;
        private Vector2 scrollPosition;
        
        // 设置参数
        private bool useRealisticData = true;
        private float scaleFactor = 0.1f;
        private float timeScale = 1f;
        
        // 材质设置
        private Color sunColor = Color.yellow;
        private Color[] planetColors = new Color[8];
        
        [MenuItem("Tools/Solar System/Solar System Editor")]
        public static void ShowWindow()
        {
            SolarSystemEditor window = GetWindow<SolarSystemEditor>("太阳系编辑器");
            window.minSize = new Vector2(400, 600);
        }
        
        void OnEnable()
        {
            // 初始化行星颜色
            planetColors[0] = new Color(0.7f, 0.7f, 0.7f); // 水星
            planetColors[1] = new Color(1f, 0.8f, 0.4f);   // 金星
            planetColors[2] = new Color(0.2f, 0.6f, 1f);   // 地球
            planetColors[3] = new Color(0.8f, 0.4f, 0.2f); // 火星
            planetColors[4] = new Color(0.9f, 0.7f, 0.4f); // 木星
            planetColors[5] = new Color(0.9f, 0.8f, 0.6f); // 土星
            planetColors[6] = new Color(0.4f, 0.8f, 0.9f); // 天王星
            planetColors[7] = new Color(0.2f, 0.4f, 0.8f); // 海王星
            
            FindSolarSystemComponents();
        }
        
        void OnGUI()
        {
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            
            GUILayout.Label("Unity 太阳系编辑器", EditorStyles.boldLabel);
            GUILayout.Space(10);
            
            DrawSetupSection();
            GUILayout.Space(10);
            
            DrawManagerSection();
            GUILayout.Space(10);
            
            DrawVisualizationSection();
            GUILayout.Space(10);
            
            DrawMaterialSection();
            GUILayout.Space(10);
            
            DrawUtilitySection();
            
            EditorGUILayout.EndScrollView();
        }
        
        /// <summary>
        /// 绘制设置部分
        /// </summary>
        private void DrawSetupSection()
        {
            GUILayout.Label("快速设置", EditorStyles.boldLabel);
            
            if (GUILayout.Button("创建完整太阳系", GUILayout.Height(30)))
            {
                CreateCompleteSolarSystem();
            }
            
            if (GUILayout.Button("创建简单太阳系", GUILayout.Height(30)))
            {
                CreateSimpleSolarSystem();
            }
            
            GUILayout.Space(5);
            
            useRealisticData = EditorGUILayout.Toggle("使用真实数据", useRealisticData);
            scaleFactor = EditorGUILayout.Slider("缩放因子", scaleFactor, 0.01f, 1f);
        }
        
        /// <summary>
        /// 绘制管理器部分
        /// </summary>
        private void DrawManagerSection()
        {
            GUILayout.Label("太阳系管理", EditorStyles.boldLabel);
            
            solarSystemManager = (SolarSystemManager)EditorGUILayout.ObjectField(
                "太阳系管理器", solarSystemManager, typeof(SolarSystemManager), true);
            
            if (solarSystemManager == null)
            {
                EditorGUILayout.HelpBox("未找到太阳系管理器，请先创建或选择一个。", MessageType.Warning);
                
                if (GUILayout.Button("查找太阳系管理器"))
                {
                    FindSolarSystemComponents();
                }
                
                if (GUILayout.Button("创建太阳系管理器"))
                {
                    CreateSolarSystemManager();
                }
            }
            else
            {
                // 显示管理器信息
                EditorGUILayout.LabelField("太阳数量", solarSystemManager.sun ? "1" : "0");
                EditorGUILayout.LabelField("行星数量", solarSystemManager.planets.Count.ToString());
                
                timeScale = EditorGUILayout.Slider("时间缩放", 
                    solarSystemManager.timeScale, 0.1f, 10f);
                
                if (GUI.changed)
                {
                    solarSystemManager.timeScale = timeScale;
                    EditorUtility.SetDirty(solarSystemManager);
                }
                
                GUILayout.BeginHorizontal();
                if (GUILayout.Button("暂停"))
                {
                    solarSystemManager.isPaused = true;
                }
                if (GUILayout.Button("继续"))
                {
                    solarSystemManager.isPaused = false;
                }
                if (GUILayout.Button("重置"))
                {
                    solarSystemManager.ResetSolarSystem();
                }
                GUILayout.EndHorizontal();
            }
        }
        
        /// <summary>
        /// 绘制可视化部分
        /// </summary>
        private void DrawVisualizationSection()
        {
            GUILayout.Label("可视化设置", EditorStyles.boldLabel);
            
            if (GUILayout.Button("显示轨道"))
            {
                ShowOrbits();
            }
            
            if (GUILayout.Button("隐藏轨道"))
            {
                HideOrbits();
            }
            
            if (GUILayout.Button("聚焦太阳"))
            {
                FocusOnSun();
            }
            
            if (GUILayout.Button("总览视角"))
            {
                SetOverviewCamera();
            }
        }
        
        /// <summary>
        /// 绘制材质部分
        /// </summary>
        private void DrawMaterialSection()
        {
            GUILayout.Label("材质设置", EditorStyles.boldLabel);
            
            sunColor = EditorGUILayout.ColorField("太阳颜色", sunColor);
            
            string[] planetNames = { "水星", "金星", "地球", "火星", "木星", "土星", "天王星", "海王星" };
            
            for (int i = 0; i < planetColors.Length; i++)
            {
                planetColors[i] = EditorGUILayout.ColorField(planetNames[i], planetColors[i]);
            }
            
            if (GUILayout.Button("应用材质颜色"))
            {
                ApplyMaterialColors();
            }
            
            if (GUILayout.Button("创建材质资源"))
            {
                CreateMaterialAssets();
            }
        }
        
        /// <summary>
        /// 绘制工具部分
        /// </summary>
        private void DrawUtilitySection()
        {
            GUILayout.Label("实用工具", EditorStyles.boldLabel);
            
            if (GUILayout.Button("创建预制体"))
            {
                CreatePrefabs();
            }
            
            if (GUILayout.Button("优化场景"))
            {
                OptimizeScene();
            }
            
            if (GUILayout.Button("清理太阳系"))
            {
                if (EditorUtility.DisplayDialog("确认", "这将删除场景中的所有太阳系对象，确定继续吗？", "确定", "取消"))
                {
                    ClearSolarSystem();
                }
            }
            
            GUILayout.Space(10);
            
            if (GUILayout.Button("打开README"))
            {
                string readmePath = "Assets/Scripts/SolarSystem/README.md";
                if (System.IO.File.Exists(readmePath))
                {
                    Application.OpenURL("file://" + System.IO.Path.GetFullPath(readmePath));
                }
            }
        }
        
        /// <summary>
        /// 查找太阳系组件
        /// </summary>
        private void FindSolarSystemComponents()
        {
            solarSystemManager = FindObjectOfType<SolarSystemManager>();
            setupTool = FindObjectOfType<SolarSystemSetup>();
            
            if (solarSystemManager)
            {
                timeScale = solarSystemManager.timeScale;
            }
        }
        
        /// <summary>
        /// 创建完整太阳系
        /// </summary>
        private void CreateCompleteSolarSystem()
        {
            if (setupTool == null)
            {
                GameObject setupObj = new GameObject("SolarSystemSetup");
                setupTool = setupObj.AddComponent<SolarSystemSetup>();
            }
            
            setupTool.useRealisticData = useRealisticData;
            setupTool.SetupSolarSystem();
            
            FindSolarSystemComponents();
            
            Debug.Log("完整太阳系创建完成！");
        }
        
        /// <summary>
        /// 创建简单太阳系
        /// </summary>
        private void CreateSimpleSolarSystem()
        {
            CreateSolarSystemManager();
            
            if (solarSystemManager)
            {
                solarSystemManager.useRealisticData = false;
                solarSystemManager.scaleFactor = scaleFactor;
            }
            
            Debug.Log("简单太阳系创建完成！");
        }
        
        /// <summary>
        /// 创建太阳系管理器
        /// </summary>
        private void CreateSolarSystemManager()
        {
            GameObject managerObj = new GameObject("SolarSystemManager");
            solarSystemManager = managerObj.AddComponent<SolarSystemManager>();
            solarSystemManager.useRealisticData = useRealisticData;
            solarSystemManager.scaleFactor = scaleFactor;
            
            Selection.activeGameObject = managerObj;
        }
        
        /// <summary>
        /// 显示轨道
        /// </summary>
        private void ShowOrbits()
        {
            // 这里可以添加轨道可视化代码
            Debug.Log("显示轨道功能待实现");
        }
        
        /// <summary>
        /// 隐藏轨道
        /// </summary>
        private void HideOrbits()
        {
            // 这里可以添加隐藏轨道代码
            Debug.Log("隐藏轨道功能待实现");
        }
        
        /// <summary>
        /// 聚焦太阳
        /// </summary>
        private void FocusOnSun()
        {
            if (solarSystemManager && solarSystemManager.sun)
            {
                Selection.activeGameObject = solarSystemManager.sun.gameObject;
                SceneView.FrameLastActiveSceneView();
            }
        }
        
        /// <summary>
        /// 设置总览相机
        /// </summary>
        private void SetOverviewCamera()
        {
            Camera mainCamera = Camera.main;
            if (mainCamera)
            {
                mainCamera.transform.position = new Vector3(0, 50, -50);
                mainCamera.transform.LookAt(Vector3.zero);
            }
        }
        
        /// <summary>
        /// 应用材质颜色
        /// </summary>
        private void ApplyMaterialColors()
        {
            // 应用太阳颜色
            if (solarSystemManager && solarSystemManager.sun)
            {
                solarSystemManager.sun.SetSunColor(sunColor);
            }
            
            // 应用行星颜色
            for (int i = 0; i < solarSystemManager.planets.Count && i < planetColors.Length; i++)
            {
                Planet planet = solarSystemManager.planets[i];
                if (planet && planet.bodyRenderer)
                {
                    planet.bodyRenderer.material.color = planetColors[i];
                }
            }
            
            Debug.Log("材质颜色已应用！");
        }
        
        /// <summary>
        /// 创建材质资源
        /// </summary>
        private void CreateMaterialAssets()
        {
            string materialPath = "Assets/Materials/SolarSystem";
            if (!AssetDatabase.IsValidFolder(materialPath))
            {
                AssetDatabase.CreateFolder("Assets/Materials", "SolarSystem");
            }
            
            // 创建太阳材质
            Material sunMaterial = new Material(Shader.Find("Standard"));
            sunMaterial.SetColor("_Color", sunColor);
            sunMaterial.SetColor("_EmissionColor", sunColor * 2f);
            sunMaterial.EnableKeyword("_EMISSION");
            AssetDatabase.CreateAsset(sunMaterial, materialPath + "/SunMaterial.mat");
            
            // 创建行星材质
            string[] planetNames = { "Mercury", "Venus", "Earth", "Mars", "Jupiter", "Saturn", "Uranus", "Neptune" };
            for (int i = 0; i < planetColors.Length; i++)
            {
                Material planetMaterial = new Material(Shader.Find("Standard"));
                planetMaterial.SetColor("_Color", planetColors[i]);
                AssetDatabase.CreateAsset(planetMaterial, materialPath + "/" + planetNames[i] + "Material.mat");
            }
            
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            
            Debug.Log("材质资源已创建！");
        }
        
        /// <summary>
        /// 创建预制体
        /// </summary>
        private void CreatePrefabs()
        {
            string prefabPath = "Assets/Prefabs/SolarSystem";
            if (!AssetDatabase.IsValidFolder(prefabPath))
            {
                AssetDatabase.CreateFolder("Assets/Prefabs", "SolarSystem");
            }
            
            // 创建基础球体预制体
            GameObject sphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            sphere.name = "CelestialBody";
            
            // 移除碰撞器
            DestroyImmediate(sphere.GetComponent<Collider>());
            
            // 保存预制体
            PrefabUtility.SaveAsPrefabAsset(sphere, prefabPath + "/CelestialBody.prefab");
            DestroyImmediate(sphere);
            
            Debug.Log("预制体已创建！");
        }
        
        /// <summary>
        /// 优化场景
        /// </summary>
        private void OptimizeScene()
        {
            // 这里可以添加场景优化代码
            Debug.Log("场景优化功能待实现");
        }
        
        /// <summary>
        /// 清理太阳系
        /// </summary>
        private void ClearSolarSystem()
        {
            SolarSystemManager[] managers = FindObjectsOfType<SolarSystemManager>();
            foreach (var manager in managers)
            {
                DestroyImmediate(manager.gameObject);
            }
            
            CelestialBody[] bodies = FindObjectsOfType<CelestialBody>();
            foreach (var body in bodies)
            {
                DestroyImmediate(body.gameObject);
            }
            
            solarSystemManager = null;
            
            Debug.Log("太阳系已清理！");
        }
    }
}
#endif
