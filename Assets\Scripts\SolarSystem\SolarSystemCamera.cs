using UnityEngine;

namespace SolarSystem
{
    /// <summary>
    /// 太阳系相机控制器，提供多种观察模式
    /// </summary>
    public class SolarSystemCamera : MonoBehaviour
    {
        [Header("相机设置")]
        public Camera mainCamera;
        public float moveSpeed = 10f;
        public float rotateSpeed = 100f;
        public float zoomSpeed = 5f;
        public float minZoom = 2f;
        public float maxZoom = 100f;
        
        [Header("跟随目标")]
        public Transform followTarget;
        public float followDistance = 10f;
        public float followHeight = 5f;
        public bool isFollowing = false;
        
        [Header("观察模式")]
        public CameraMode currentMode = CameraMode.Free;
        public Transform[] viewTargets;
        public int currentTargetIndex = 0;
        
        [Header("自动旋转")]
        public bool autoRotate = false;
        public float autoRotateSpeed = 10f;
        public Vector3 autoRotateCenter = Vector3.zero;
        
        private Vector3 lastMousePosition;
        private bool isDragging = false;
        private float currentZoom;
        
        public enum CameraMode
        {
            Free,       // 自由模式
            Follow,     // 跟随模式
            Orbit,      // 轨道模式
            Overview    // 总览模式
        }
        
        void Start()
        {
            if (mainCamera == null)
                mainCamera = GetComponent<Camera>();
            
            currentZoom = Vector3.Distance(transform.position, Vector3.zero);
            
            // 设置初始位置
            SetOverviewMode();
        }
        
        void Update()
        {
            HandleInput();
            UpdateCameraMode();
        }
        
        /// <summary>
        /// 处理输入
        /// </summary>
        private void HandleInput()
        {
            // 鼠标控制
            HandleMouseInput();
            
            // 键盘控制
            HandleKeyboardInput();
            
            // 滚轮缩放
            HandleZoom();
            
            // 模式切换
            HandleModeSwitch();
        }
        
        /// <summary>
        /// 处理鼠标输入
        /// </summary>
        private void HandleMouseInput()
        {
            if (Input.GetMouseButtonDown(0))
            {
                lastMousePosition = Input.mousePosition;
                isDragging = true;
            }
            
            if (Input.GetMouseButtonUp(0))
            {
                isDragging = false;
            }
            
            if (isDragging && currentMode == CameraMode.Free)
            {
                Vector3 deltaPosition = Input.mousePosition - lastMousePosition;
                
                // 水平旋转
                transform.RotateAround(Vector3.zero, Vector3.up, -deltaPosition.x * rotateSpeed * Time.deltaTime);
                
                // 垂直旋转
                transform.RotateAround(Vector3.zero, transform.right, deltaPosition.y * rotateSpeed * Time.deltaTime);
                
                lastMousePosition = Input.mousePosition;
            }
        }
        
        /// <summary>
        /// 处理键盘输入
        /// </summary>
        private void HandleKeyboardInput()
        {
            if (currentMode == CameraMode.Free)
            {
                Vector3 moveDirection = Vector3.zero;
                
                if (Input.GetKey(KeyCode.W)) moveDirection += transform.forward;
                if (Input.GetKey(KeyCode.S)) moveDirection -= transform.forward;
                if (Input.GetKey(KeyCode.A)) moveDirection -= transform.right;
                if (Input.GetKey(KeyCode.D)) moveDirection += transform.right;
                if (Input.GetKey(KeyCode.Q)) moveDirection += transform.up;
                if (Input.GetKey(KeyCode.E)) moveDirection -= transform.up;
                
                transform.position += moveDirection * moveSpeed * Time.deltaTime;
            }
        }
        
        /// <summary>
        /// 处理缩放
        /// </summary>
        private void HandleZoom()
        {
            float scroll = Input.GetAxis("Mouse ScrollWheel");
            if (Mathf.Abs(scroll) > 0.01f)
            {
                currentZoom -= scroll * zoomSpeed;
                currentZoom = Mathf.Clamp(currentZoom, minZoom, maxZoom);
                
                if (currentMode == CameraMode.Free || currentMode == CameraMode.Overview)
                {
                    Vector3 direction = (transform.position - Vector3.zero).normalized;
                    transform.position = direction * currentZoom;
                }
            }
        }
        
        /// <summary>
        /// 处理模式切换
        /// </summary>
        private void HandleModeSwitch()
        {
            if (Input.GetKeyDown(KeyCode.F1)) SetFreeMode();
            if (Input.GetKeyDown(KeyCode.F2)) SetFollowMode();
            if (Input.GetKeyDown(KeyCode.F3)) SetOrbitMode();
            if (Input.GetKeyDown(KeyCode.F4)) SetOverviewMode();
            
            // 切换跟随目标
            if (Input.GetKeyDown(KeyCode.Tab))
            {
                SwitchTarget();
            }
            
            // 切换自动旋转
            if (Input.GetKeyDown(KeyCode.R))
            {
                autoRotate = !autoRotate;
            }
        }
        
        /// <summary>
        /// 更新相机模式
        /// </summary>
        private void UpdateCameraMode()
        {
            switch (currentMode)
            {
                case CameraMode.Follow:
                    UpdateFollowMode();
                    break;
                case CameraMode.Orbit:
                    UpdateOrbitMode();
                    break;
                case CameraMode.Overview:
                    UpdateOverviewMode();
                    break;
            }
            
            if (autoRotate)
            {
                UpdateAutoRotate();
            }
        }
        
        /// <summary>
        /// 更新跟随模式
        /// </summary>
        private void UpdateFollowMode()
        {
            if (followTarget == null) return;
            
            Vector3 targetPosition = followTarget.position - followTarget.forward * followDistance + Vector3.up * followHeight;
            transform.position = Vector3.Lerp(transform.position, targetPosition, Time.deltaTime * 2f);
            transform.LookAt(followTarget.position);
        }
        
        /// <summary>
        /// 更新轨道模式
        /// </summary>
        private void UpdateOrbitMode()
        {
            if (followTarget == null) return;
            
            transform.RotateAround(followTarget.position, Vector3.up, autoRotateSpeed * Time.deltaTime);
            transform.LookAt(followTarget.position);
        }
        
        /// <summary>
        /// 更新总览模式
        /// </summary>
        private void UpdateOverviewMode()
        {
            // 保持俯视角度观察整个太阳系
            Vector3 overviewPosition = Vector3.up * currentZoom * 0.5f + Vector3.back * currentZoom * 0.5f;
            transform.position = Vector3.Lerp(transform.position, overviewPosition, Time.deltaTime);
            transform.LookAt(Vector3.zero);
        }
        
        /// <summary>
        /// 更新自动旋转
        /// </summary>
        private void UpdateAutoRotate()
        {
            transform.RotateAround(autoRotateCenter, Vector3.up, autoRotateSpeed * Time.deltaTime);
        }
        
        /// <summary>
        /// 设置自由模式
        /// </summary>
        public void SetFreeMode()
        {
            currentMode = CameraMode.Free;
            autoRotate = false;
        }
        
        /// <summary>
        /// 设置跟随模式
        /// </summary>
        public void SetFollowMode()
        {
            currentMode = CameraMode.Follow;
            if (followTarget == null && viewTargets.Length > 0)
            {
                followTarget = viewTargets[currentTargetIndex];
            }
        }
        
        /// <summary>
        /// 设置轨道模式
        /// </summary>
        public void SetOrbitMode()
        {
            currentMode = CameraMode.Orbit;
            if (followTarget == null && viewTargets.Length > 0)
            {
                followTarget = viewTargets[currentTargetIndex];
            }
        }
        
        /// <summary>
        /// 设置总览模式
        /// </summary>
        public void SetOverviewMode()
        {
            currentMode = CameraMode.Overview;
            autoRotate = false;
            currentZoom = 50f;
        }
        
        /// <summary>
        /// 切换目标
        /// </summary>
        public void SwitchTarget()
        {
            if (viewTargets.Length == 0) return;
            
            currentTargetIndex = (currentTargetIndex + 1) % viewTargets.Length;
            followTarget = viewTargets[currentTargetIndex];
        }
        
        /// <summary>
        /// 设置跟随目标
        /// </summary>
        public void SetFollowTarget(Transform target)
        {
            followTarget = target;
        }
        
        /// <summary>
        /// 聚焦到目标
        /// </summary>
        public void FocusOnTarget(Transform target)
        {
            followTarget = target;
            SetFollowMode();
        }
    }
}
